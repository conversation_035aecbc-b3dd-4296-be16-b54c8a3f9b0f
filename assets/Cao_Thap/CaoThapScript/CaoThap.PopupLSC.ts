
import { _decorator, instantiate, Label, RichText, Sprite, SpriteFrame, ToggleContainer, Node } from "cc";
import Dialog from "../../Lobby/scripts/common/Dialog";
import { Utils } from "../../Lobby/scripts/common/Utils";
import App from "../../Lobby/scripts/common/App";
import Http from "../../Lobby/scripts/common/Http";
import Configs from "../../Lobby/scripts/common/Config";
import Pagination from "../../Lobby/scripts/common/Pagination";

const { ccclass, property } = _decorator;

//S<PERSON>a lại lần lư<PERSON>: <PERSON><PERSON><PERSON>, Bước, Kết qu<PERSON>, cửa đặt, Nhận

@ccclass
export class CaoThapPopupLSC extends Dialog {

    @property(ToggleContainer)
    private tabs: ToggleContainer = null;

    @property([Node])
    private tabsContent: Node[] = [];

    @property(Node) private contentTipzo: Node = null;
    @property(Node) private rowTipzo: Node = null;
    @property(Pagination) private paginationTipzo: Pagination = null;

    @property(Node) private contentXu: Node = null;
    @property(Node) private rowXu: Node = null;
    @property(Pagination) private paginationXu: Pagination = null;

    @property(SpriteFrame) private rowBackground1: SpriteFrame = null;
    @property(SpriteFrame) private rowBackground2: SpriteFrame = null;

    private _dataTipzoLoaded = null;
    private _dataXuLoaded = null;

    onLoad() {
        this.tabs.toggleItems.forEach((tab, index) => {
            tab.node.on("toggle", () => {
                this.onTabChanged(index);
            })
        })
        this.paginationTipzo.initListener((page: number) => {
            this.showTipzoHistory(page);
        });

        this.paginationXu.initListener((page: number) => {
            this.showXuHistory(page);
        });
    }

    public show() {
        super.show();
        this.fetchTipzoHistory();
        this.fetchXuHistory();
    }

    private onTabChanged(index: number) {
        if (index < 0 || index >= this.tabsContent.length) return;

        this.tabsContent.forEach(tab => tab.active = false);
        this.tabsContent[index].active = true;

        const showContentCallback = {
            [0]: () => this.showTipzoHistory(1),
            [1]: () => this.showXuHistory(1),
        };
        showContentCallback[index]?.();
    }

    private showTipzoHistory(page: number) {
        this.contentTipzo.removeAllChildren();
        this.paginationTipzo.updatePagination(Math.ceil(this._dataTipzoLoaded.length / 10), page);

        let ithRow = 0;
        let loaded = this._dataTipzoLoaded.slice((page - 1) * 10, page * 10);
        for (let data of loaded) {
            let itemRow = instantiate(this.rowTipzo);
            itemRow.active = true;
            this.contentTipzo.addChild(itemRow);
            itemRow.children[0].getComponent(Label).string = data.TurnID;
            itemRow.children[1].getComponent(Label).string = Utils.formatDatetime(data.CreatedTime, "dd/MM/yyyy HH:mm:ss");
            itemRow.children[2].getComponent(Label).string = Utils.formatNumber(data.BetValue);
            itemRow.children[3].getComponent(Label).string = data.Step;
            itemRow.children[4].getComponent(RichText).string = data.CardID >= 0 ? convertCardSymbols(data.CardID.toString()) : "";
            if (data.Step == 1) {
                itemRow.children[5].getComponent(Label).string = "";
                itemRow.children[6].getComponent(Label).string = `-${Utils.formatNumber(data.PrizeValue)}`
            } else {
                itemRow.children[5].getComponent(Label).string = data.LocationID == 0 ? App.instance.getTextLang('hl14') : App.instance.getTextLang('hl13');
                itemRow.children[6].getComponent(Label).string = `${Utils.formatNumber(data.PrizeValue)}`;
            }
            itemRow.getComponent(Sprite).spriteFrame = ithRow % 2 === 0 ? this.rowBackground1 : this.rowBackground2;
            ithRow++;
        }
    }

    private showXuHistory(page: number) {
        this.contentXu.removeAllChildren();
        this.paginationXu.updatePagination(Math.ceil(this._dataXuLoaded.length / 10), page);

        let ithRow = 0;
        let loaded = this._dataXuLoaded.slice((page - 1) * 10, page * 10);
        for (let data of loaded) {
            let itemRow = instantiate(this.rowXu);
            itemRow.active = true;
            this.contentXu.addChild(itemRow);
            itemRow.children[0].getComponent(Label).string = data.TurnID;
            itemRow.children[1].getComponent(Label).string = Utils.formatDatetime(data.CreatedTime, "dd/MM/yyyy HH:mm:ss");
            itemRow.children[2].getComponent(Label).string = Utils.formatNumber(data.BetValue);
            itemRow.children[3].getComponent(Label).string = data.Step;
            itemRow.children[4].getComponent(RichText).string = data.CardID >= 0 ? convertCardSymbols(data.CardID.toString()) : "";
            if (data.Step == 1) {
                itemRow.children[5].getComponent(Label).string = "";
                itemRow.children[6].getComponent(Label).string = `-${Utils.formatNumber(data.PrizeValue)}`
            } else {
                itemRow.children[5].getComponent(Label).string = data.LocationID == 0 ? App.instance.getTextLang('hl14') : App.instance.getTextLang('hl13');
                itemRow.children[6].getComponent(Label).string = `${Utils.formatNumber(data.PrizeValue)}`;
            }
            itemRow.getComponent(Sprite).spriteFrame = ithRow % 2 === 0 ? this.rowBackground1 : this.rowBackground2;
            ithRow++;
        }
    }

    public dismiss() {
        super.dismiss();
    }

    private fetchTipzoHistory() {
        Http.get(Configs.App.DOMAIN_CONFIG["GetAccountHistoryHiLo"], {
            "currencyID": Configs.Login.CurrencyID,
            "betType": 1,
            "topCount": 100,
        }, (status, json) => {
            if (json["c"] >= 0) {
                this._dataTipzoLoaded = json["d"];
                this.showTipzoHistory(1);
            }
        }
        );
    }

    private fetchXuHistory() {
        Http.get(Configs.App.DOMAIN_CONFIG["GetAccountHistoryHiLo"], {
            "currencyID": Configs.Login.CurrencyID,
            "betType": 2,
            "topCount": 100,
        }, (status, json) => {
            if (json["c"] >= 0) {
                this._dataXuLoaded = json["d"];
                this.showXuHistory(1);
            }
        }
        );
    }

}


function convertCardSymbols(cardIds: string) {
    if (cardIds === "") return "";

    const ranks = ["2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K", "A"];
    const suits = ["♠", "♣", "♦", "♥"]; // Spades, Clubs, Diamonds, Hearts

    return cardIds.replace(/\d+/g, (match) => {
        const cardId = parseInt(match);
        if (isNaN(cardId) || cardId < 0 || cardId > 51) return match; // Return original if invalid
        const suitIndex = Math.floor(cardId / 13);
        const rankIndex = cardId % 13;

        const suit = suits[suitIndex];
        const rank = ranks[rankIndex];

        return `${rank}${suit}`;
    });
}

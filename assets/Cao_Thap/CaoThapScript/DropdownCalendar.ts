import { _decorator, Button, Component, instantiate, Label, Node, Toggle, UIOpacity } from "cc";

const {ccclass, property, menu} = _decorator;

export interface Listener {
    onChangeDate?(date: Date): void;
}

@ccclass('DropdownCalendar')
@menu("CustomUI/DropdownCalendar")
export class DropdownCalendar extends Component {
    @property(Node) calendarPanel: Node = null;
    @property(Label) private selectedDateLabel: Label = null;
    @property(Node) private dayContainer: Node = null;
    @property(Label) private headerLabel: Label = null;
    @property(Node) private dayTemplate: Node = null;

    private currentDate: Date = new Date();
    private _listener: Listener = {};

    start() {
        this.calendarPanel.active = false;
        this.updateCalendar();
        this.selectedDateLabel.string = this.formatDate(this.currentDate);

        this.initCurrentDate();
    }

    public setListener(listener: Listener) {
        this._listener = listener;
    }

    private toggleCalendar() {
        this.calendarPanel.active = !this.calendarPanel.active;
    }

    private updateCalendar() {
        let year = this.currentDate.getFullYear();
        let month = this.currentDate.getMonth();
        let firstDay = new Date(year, month, 1).getDay();
        let daysInMonth = new Date(year, month + 1, 0).getDate();

        this.headerLabel.string = this.formatDate(this.currentDate);

        this.dayContainer.removeAllChildren();

        for (let i = 0; i < firstDay; i++) {
            let emptyNode = instantiate(this.dayTemplate);
            emptyNode.active = true;
            emptyNode.getComponent(UIOpacity).opacity = 0;
            this.dayContainer.addChild(emptyNode);
        }

        for (let day = 1; day <= daysInMonth; day++) {
            let dayNode = instantiate(this.dayTemplate);
            dayNode.active = true;
            dayNode.getComponent(UIOpacity).opacity = 255;
            dayNode.on(Node.EventType.TOUCH_START, () => {
                this.selectDate(day);
            });
            let label = dayNode.getComponentInChildren(Label)
            label.string = day.toString().padStart(2, '0');
            this.dayContainer.addChild(dayNode);
        }
    }

    public selectDate(day: number) {
        this.currentDate.setDate(day);
        this.selectedDateLabel.string = this.formatDate(this.currentDate);
        this._listener.onChangeDate && this._listener.onChangeDate(this.currentDate);
    }

    initCurrentDate(){
        let dayOfMonth = this.currentDate.getDate();
        for(let i = 0; i < this.dayContainer.children.length; i++){
            if(i == dayOfMonth - 1){
                this.dayContainer.children[i].getComponent(Toggle).isChecked = true;
                break;
            }
        }
    }

    private changeMonth(delta: number) {
        this.currentDate.setMonth(this.currentDate.getMonth() + delta);
        this.updateCalendar();
    }

    private formatDate(date: Date): string {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Ensure two digits
        const day = date.getDate().toString().padStart(2, '0'); // Ensure two digits
        return `${day}/${month}/${year}`;
    }

    private onClickNavigate(button: Button, step: string) {
        this.changeMonth(parseInt(step));
    }
}

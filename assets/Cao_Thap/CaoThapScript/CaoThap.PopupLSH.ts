import { _decorator, instantiate, Label, Node, Sprite, SpriteFrame, ToggleContainer } from "cc";
import Dialog from "../../Lobby/scripts/common/Dialog";
import { Utils } from "../../Lobby/scripts/common/Utils";
import App from "../../Lobby/scripts/common/App";
import Http from "../../Lobby/scripts/common/Http";
import Configs from "../../Lobby/scripts/common/Config";
import Pagination from "../../Lobby/scripts/common/Pagination";

const {ccclass, property} = _decorator;

@ccclass
export class CaoThapPopupLSH extends Dialog {
    @property(ToggleContainer)
    private tabs: ToggleContainer = null;

    @property([Node])
    private tabsContent: Node[] = [];

    @property(Node) private contentTipzo: Node = null;
    @property(Node) private rowTipzo: Node = null;
    @property(Pagination) private paginationTipzo: Pagination = null;


    @property(Node) private contentXu: Node = null;
    @property(Node) private rowXu: Node = null;
    @property(Pagination) private paginationXu: Pagination = null;
    
    @property(SpriteFrame) private rowBackground1: SpriteFrame = null;
    @property(SpriteFrame) private rowBackground2: SpriteFrame = null;

    private _dataTipzoLoaded = null;
    private _dataXuLoaded = null;

    onLoad() {
        this.tabs.toggleItems.forEach((tab, index) => {
            tab.node.on("toggle", () => {
                this.onTabChanged(index);
            })
        })

        this.paginationTipzo.initListener((page: number) => {
            this.showTipzoHistory(page);
        });

        this.paginationXu.initListener((page: number) => {
            this.showXuHistory(page);
        });
    }

    public show() {
        super.show();
        
        this.fetchTipzoHistory();
        this.fetchXuHistory();
    }

    private onTabChanged(index: number) {
        if (index < 0 || index >= this.tabsContent.length) return;

        this.tabsContent.forEach(tab => tab.active = false);
        this.tabsContent[index].active = true;
    }

    private showTipzoHistory(page: number) {
        this.contentTipzo.removeAllChildren();
        this.paginationTipzo.updatePagination(Math.ceil(this._dataTipzoLoaded.length / 10), page);

        let ithRow = 0;
        let loaded = this._dataTipzoLoaded.slice((page - 1) * 10, page * 10);
        for (let data of loaded) {
            let itemRow = instantiate(this.rowTipzo);
            itemRow.active = true;
            this.contentTipzo.addChild(itemRow);
            itemRow.children[0].getComponent(Label).string = Utils.formatDatetime(data.CreatedTime, "dd/MM/yyyy HH:mm:ss");
            itemRow.children[1].getComponent(Label).string = data.Nickname;
            itemRow.children[2].getComponent(Label).string = Utils.formatNumberMin(this.getRoomValue(1, data.RoomID)).toString();
            itemRow.children[3].getComponent(Label).string = Utils.formatNumber(data.PrizeValue);
            itemRow.children[4].getComponent(Label).string = data.Type == 0 ? App.instance.getTextLang('sl16') : App.instance.getTextLang('sl15');
            itemRow.getComponent(Sprite).spriteFrame = ithRow % 2 === 0 ? this.rowBackground1 : this.rowBackground2;
            ithRow++;
        }
    }

    private showXuHistory(page: number) {
        this.contentXu.removeAllChildren();
        this.paginationXu.updatePagination(Math.ceil(this._dataXuLoaded.length / 10), page);

        let ithRow = 0;
        let loaded = this._dataXuLoaded.slice((page - 1) * 10, page * 10);
        for (let data of loaded) {
            let itemRow = instantiate(this.rowXu);
            itemRow.active = true;
            this.contentXu.addChild(itemRow);
            itemRow.children[0].getComponent(Label).string = Utils.formatDatetime(data.CreatedTime, "dd/MM/yyyy HH:mm:ss");
            itemRow.children[1].getComponent(Label).string = data.Nickname;
            itemRow.children[2].getComponent(Label).string = Utils.formatNumberMin(this.getRoomValue(2, data.RoomID)).toString();
            itemRow.children[3].getComponent(Label).string = Utils.formatNumber(data.PrizeValue);
            itemRow.children[4].getComponent(Label).string = data.Type == 0 ? App.instance.getTextLang('sl16') : App.instance.getTextLang('sl15');
            itemRow.getComponent(Sprite).spriteFrame = ithRow % 2 === 0 ? this.rowBackground1 : this.rowBackground2;
            ithRow++;
        }
    }

    dismiss() {
        super.dismiss();
    }

    private fetchTipzoHistory() {
        Http.get(Configs.App.DOMAIN_CONFIG["GetTopWinnersHiLo"], {
            "currencyID": Configs.Login.CurrencyID,
            "betType": 1,
            "topCount": 100,
        }, (status, json) => {
            if (json["c"] >= 0) {
                this._dataTipzoLoaded = json["d"];
                this.showTipzoHistory(1);
            }
        }
        );
    }

    private fetchXuHistory() {
        Http.get(Configs.App.DOMAIN_CONFIG["GetTopWinnersHiLo"], {
            "currencyID": Configs.Login.CurrencyID,
            "betType": 2,
            "topCount": 100,
        }, (status, json) => {
            if (json["c"] >= 0) {
                this._dataXuLoaded = json["d"];
                this.showXuHistory(1);
            }
        }
        );
    }

    getRoomValue(betType: number, roomID: number) {
        if (betType === 1) {
            return [0, 1_000, 10_000, 50_000, 100_000, 500_000][roomID];
        } else if (betType === 2) {
            return [0, 10_000, 100_000, 500_000, 1_000_000, 5_000_000][roomID];
        }
    }
}


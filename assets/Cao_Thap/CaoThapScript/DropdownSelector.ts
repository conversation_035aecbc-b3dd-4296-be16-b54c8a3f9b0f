import { _decorator,Node, Component, Label, instantiate } from "cc";

const {ccclass, property, menu} = _decorator;

export interface Listener<T> {
    onChangeOption?(option: T): void;
}

@ccclass('DropdownSelector')
@menu("CustomUI/DropdownSelector")
export class DropdownSelector extends Component {
    @property(Node) dropdownList: Node = null;
    @property(Label) private selectedText: Label = null;
    @property(Node) private content: Node = null;
    @property(Node) private optionTemplate: Node = null;

    private options: any[] = [];
    private displayFunction: (item: any) => string = String;
    private _listener: Listener<any> = {};

    start() {
        this.dropdownList.active = false;

    }
    
    toggleDropdown() {
        this.dropdownList.active = !this.dropdownList.active;
        if(this.dropdownList.active) {
            this.populateOptions();
        }
    }
    
    public setOptions<T>(
        options: T[],
        displayFunc: (item: T) => string = String,
        callback: Listener<T>
    ) {
        this.options = options;
        this.displayFunction = displayFunc;
        this._listener = callback || {};
        this.populateOptions();
        this.selectOption(options[0]);
    }

    private populateOptions() {
        this.content.removeAllChildren();

        this.options.forEach(option => {
            let item = instantiate(this.optionTemplate);
            item.active = true;
            let label = item.getComponentInChildren(Label);
            label.string = this.displayFunction(option);

            item.on(Node.EventType.TOUCH_START, () => {
                this.selectOption(option);
            });
            this.content.addChild(item);
        });
    }

    public selectOption(option: any) {
        this.selectedText.string = this.displayFunction(option);
        this.dropdownList.active = false; // Hide dropdown
        this._listener.onChangeOption && this._listener.onChangeOption(option);
    }
}

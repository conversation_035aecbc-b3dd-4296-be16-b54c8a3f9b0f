[{"__type__": "cc.AnimationClip", "_name": "jackpot", "_objFlags": 0, "_native": "", "sample": 60, "speed": 1, "wrapMode": 1, "_duration": 3, "_hash": 0, "_tracks": [{"__id__": 1}, {"__id__": 12}, {"__id__": 17}], "_events": [], "_exoticAnimation": null}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 2}}, "_channels": [{"__id__": 4}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 3}, "eulerAngles"]}, {"__type__": "cc.animation.HierarchyPath", "path": "ct-light"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 5}}, {"__type__": "cc.RealCurve", "_times": [0, 3], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 7}}, {"__type__": "cc.RealCurve", "_times": [0, 3], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 9}}, {"__type__": "cc.RealCurve", "_times": [0, 3], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 720, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 11}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.ObjectTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 13}}, "_channel": {"__id__": 15}}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 14}, "active"]}, {"__type__": "cc.animation.HierarchyPath", "path": "ct-light"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 16}}, {"__type__": "cc.ObjectCurve", "_times": [0, 2.75], "_values": [true, false]}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 18}}, "_channels": [{"__id__": 20}, {"__id__": 22}, {"__id__": 24}, {"__id__": 26}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 19}, "scale"]}, {"__type__": "cc.animation.HierarchyPath", "path": "ct-gt-vi"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 21}}, {"__type__": "cc.RealCurve", "_times": [0, 0.5, 2.5, 2.75], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 28, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1.2, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 26, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1.1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 23}}, {"__type__": "cc.RealCurve", "_times": [0, 0.5, 2.5, 2.75], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 28, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1.2, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 26, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1.1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 25}}, {"__type__": "cc.RealCurve", "_times": [0, 0.5, 2.5, 2.75], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 28, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 26, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 27}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}]
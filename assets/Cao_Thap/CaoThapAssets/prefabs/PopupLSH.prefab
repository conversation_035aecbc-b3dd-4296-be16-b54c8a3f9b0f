[{"__type__": "cc.Prefab", "_name": "PopupLSH", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false, "persistent": false}, {"__type__": "cc.Node", "_name": "PopupLSH", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 14}], "_active": true, "_components": [{"__id__": 529}, {"__id__": 531}, {"__id__": 533}, {"__id__": 535}, {"__id__": 537}], "_prefab": {"__id__": 539}, "_lpos": {"__type__": "cc.Vec3", "x": 960, "y": 540, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}, {"__id__": 9}, {"__id__": 11}], "_prefab": {"__id__": 13}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "507CM/FvBN1Y1hEDa9gN37"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1523.8, "_originalHeight": 857.2, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcAaw7WnZGtrDhPLFp18lY"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "182ny/yERPi6dkU01d171I"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 10}, "_opacity": 100}, {"__type__": "cc.CompPrefabInfo", "fileId": "542vdSDbJK8odADdhbxEiS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 12}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "88r8kQL5NHyJlRpfwR9SXV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "06DaKdG/5A+ZHNtTJqWa9+"}, {"__type__": "cc.Node", "_name": "Container", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 15}, {"__id__": 27}, {"__id__": 40}, {"__id__": 114}, {"__id__": 204}, {"__id__": 363}], "_active": true, "_components": [{"__id__": 522}, {"__id__": 524}, {"__id__": 526}], "_prefab": {"__id__": 528}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "TITLE", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 16}, {"__id__": 18}, {"__id__": 20}, {"__id__": 22}, {"__id__": 24}], "_prefab": {"__id__": 26}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 452.3, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 17}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "THÀNH TÍCH", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eae4bRgV9PPbu37qsLkBWO"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "id": "ca169", "isUpperCase": true, "useCustomFont": true, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 19}}, {"__type__": "cc.CompPrefabInfo", "fileId": "5d9mZ5B1FEzoMJaCGiHASA"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 21}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 50, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdHEoDg3FBa40tNPJArdyB"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 23}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "11Srogu/ZES5ULKFEN670A"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 25}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 209.44, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "adQP3WKLZK3KtUiO0Mz3Nq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d2jKtQ9NhOoaYITw3AZ7HT"}, {"__type__": "cc.Node", "_name": "BtnClose", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 28}, {"__id__": 30}, {"__id__": 33}, {"__id__": 35}, {"__id__": 37}], "_prefab": {"__id__": 39}, "_lpos": {"__type__": "cc.Vec3", "x": 820, "y": 507.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 29}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7a782e51-5907-4722-b5aa-e93a8b6e57cc@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6t1yWCqFPNJxoLxc9zuIS"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 31}, "clickEvents": [{"__id__": 32}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.95, "_target": {"__id__": 27}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "07d3APFR1EiYa4W9J3AOGv"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "0ae26IAIF5KZ6qO8QyYrJWa", "handler": "dismiss", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 34}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 10, "_top": 20, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3Kro+fLVJlIYdPCBRubMS"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 36}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "72fUZkZKJJmo7WG4dI8jMA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 38}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 162, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"__type__": "cc.CompPrefabInfo", "fileId": "51GCUC2y9D5IF5jND+/HVL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "28QdZDxAhEr7MwU/NTbSAi"}, {"__type__": "cc.Node", "_name": "bg_title", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [{"__id__": 41}, {"__id__": 73}], "_active": true, "_components": [{"__id__": 105}, {"__id__": 107}, {"__id__": 109}, {"__id__": 111}], "_prefab": {"__id__": 113}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 360, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "tab1", "_objFlags": 0, "_parent": {"__id__": 40}, "_children": [{"__id__": 42}, {"__id__": 50}], "_active": true, "_components": [{"__id__": 66}, {"__id__": 68}, {"__id__": 70}], "_prefab": {"__id__": 72}, "_lpos": {"__type__": "cc.Vec3", "x": -139.766, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 41}, "_children": [], "_active": true, "_components": [{"__id__": 43}, {"__id__": 45}, {"__id__": 47}], "_prefab": {"__id__": 49}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2.32, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "__prefab": {"__id__": 44}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 205, "g": 190, "b": 228, "a": 255}, "_useOriginalSize": true, "_string": "TIPZO", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "637245a9-2795-4858-9a4e-4b0a8a49c7c3"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3aUtB7ro1B0KLb0jvvhgwg"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "__prefab": {"__id__": 46}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "36fn0onndA1JhZbDkUHcfv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "__prefab": {"__id__": 48}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 77.11, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "56L+4pAUREkYpzBiuT3qzf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dfpT7dh15Oh6P+vwFpRnRk"}, {"__type__": "cc.Node", "_name": "btn_tit", "_objFlags": 0, "_parent": {"__id__": 41}, "_children": [{"__id__": 51}], "_active": true, "_components": [{"__id__": 59}, {"__id__": 61}, {"__id__": 63}], "_prefab": {"__id__": 65}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2.32, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 50}, "_children": [], "_active": true, "_components": [{"__id__": 52}, {"__id__": 54}, {"__id__": 56}], "_prefab": {"__id__": 58}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "__prefab": {"__id__": 53}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 240, "b": 0, "a": 255}, "_useOriginalSize": true, "_string": "TIPZO", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "637245a9-2795-4858-9a4e-4b0a8a49c7c3"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45IByUvztJn7+uck87rdWj"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "__prefab": {"__id__": 55}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8sDfNQfpMcpYRiWlE7EoP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "__prefab": {"__id__": 57}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 77.11, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcp3mAQ0RLrpT0ol3d9jGi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "01TKGArdNOyKyJnmHTZhVQ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 60}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9b80e41b-34f8-450c-87d7-4e8b01646b27@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29kCaYiNVFXpmO7PzWr2od"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 62}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "15TMUskGlIYpLEzyZpKfj4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 64}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 298, "height": 93}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "64mKj4vs9Pi4sWpixc1RDx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a7I2O9s9BMeJBW+77QVMuS"}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 67}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 41}, "checkEvents": [], "_isChecked": true, "_checkMark": {"__id__": 59}, "_N$enableAutoGrayEffect": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fr7aSrZhJspumOmgfNiy7"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 69}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "34rP4UyGJG2qrC0IEZ6Caa"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 71}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 298, "height": 93}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "9cNWg8sb5BYr+pWzePfBMI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "572XQaUR1CY5O06X0R095P"}, {"__type__": "cc.Node", "_name": "tab2", "_objFlags": 0, "_parent": {"__id__": 40}, "_children": [{"__id__": 74}, {"__id__": 82}], "_active": true, "_components": [{"__id__": 98}, {"__id__": 100}, {"__id__": 102}], "_prefab": {"__id__": 104}, "_lpos": {"__type__": "cc.Vec3", "x": 143.217, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 73}, "_children": [], "_active": true, "_components": [{"__id__": 75}, {"__id__": 77}, {"__id__": 79}], "_prefab": {"__id__": 81}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2.32, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 76}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 205, "g": 190, "b": 228, "a": 255}, "_useOriginalSize": true, "_string": "XU", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "637245a9-2795-4858-9a4e-4b0a8a49c7c3"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "478rY/yi1BYbS27jj2GHrL"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 78}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "45FsGwBf9Nv6+zwbvHr3Rv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 80}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 37.05, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "79r0bjKphAeZzNX96Z87od"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "11jcY0k/BGRppII41aS9Ez"}, {"__type__": "cc.Node", "_name": "btn_tit", "_objFlags": 0, "_parent": {"__id__": 73}, "_children": [{"__id__": 83}], "_active": false, "_components": [{"__id__": 91}, {"__id__": 93}, {"__id__": 95}], "_prefab": {"__id__": 97}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2.32, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 82}, "_children": [], "_active": true, "_components": [{"__id__": 84}, {"__id__": 86}, {"__id__": 88}], "_prefab": {"__id__": 90}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "__prefab": {"__id__": 85}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 240, "b": 0, "a": 255}, "_useOriginalSize": true, "_string": "XU", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "637245a9-2795-4858-9a4e-4b0a8a49c7c3"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33II6grCNK6bnNvlzq+a28"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "__prefab": {"__id__": 87}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "6040Z9cLlBS6hxaC5keffU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "__prefab": {"__id__": 89}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 37.05, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "eaWceqFlVFVZ0O+6oFoDEa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b3DO1uVvFNrLPsuUky3AUP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 92}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9b80e41b-34f8-450c-87d7-4e8b01646b27@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9193jtZVA/JuqEdiVp4uy"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 94}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8DksW+w1LDLw2YHeisFFp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 96}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 298, "height": 93}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fcQkkveNDhIWYyeFJrxdx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "efPuHQIVdFn6EkQkndG0PS"}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "__prefab": {"__id__": 99}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 73}, "checkEvents": [], "_isChecked": false, "_checkMark": {"__id__": 91}, "_N$enableAutoGrayEffect": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4ZYXg9fNPrYy6+r9w+8ib"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "__prefab": {"__id__": 101}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eJkhfT0tN3rOoABxNS6iI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "__prefab": {"__id__": 103}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 298, "height": 93}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "04TMk1s6hIjZYXYpXZzpui"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2fcFHaHqRHh4W9IYQdrCAe"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "__prefab": {"__id__": 106}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "99a11394-5e45-441b-88f1-93849e135d60@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5InV23BhLeJAalvdYTTpu"}, {"__type__": "cc.ToggleContainer", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "__prefab": {"__id__": 108}, "_allowSwitchOff": false, "checkEvents": [], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8QWVwbZBGkp8DuL87VQcS"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "__prefab": {"__id__": 110}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3uhtLTAFKirTtRKm/mY9r"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "__prefab": {"__id__": 112}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 560, "height": 72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "22KYG7l91CUY/VnAmiJ6+l"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4e560x89AtqpdMhUFmdiR"}, {"__type__": "cc.Node", "_name": "header", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [{"__id__": 115}, {"__id__": 131}, {"__id__": 147}, {"__id__": 163}, {"__id__": 179}], "_active": true, "_components": [{"__id__": 195}, {"__id__": 197}, {"__id__": 199}, {"__id__": 201}], "_prefab": {"__id__": 203}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 280, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "head", "_objFlags": 0, "_parent": {"__id__": 114}, "_children": [{"__id__": 116}], "_active": true, "_components": [{"__id__": 126}, {"__id__": 128}], "_prefab": {"__id__": 130}, "_lpos": {"__type__": "cc.Vec3", "x": -624.8, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 115}, "_children": [], "_active": true, "_components": [{"__id__": 117}, {"__id__": 119}, {"__id__": 121}, {"__id__": 123}], "_prefab": {"__id__": 125}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 116}, "_enabled": true, "__prefab": {"__id__": 118}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON> gian", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08Nj+h/0BADImoRchJbNjZ"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 116}, "_enabled": true, "id": "TITLE_TIME", "isUpperCase": true, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 120}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b9YQ8Bt0hOgqUYxTsW0VPq"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 116}, "_enabled": true, "__prefab": {"__id__": 122}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "feVg/pWkJFhLKl6A3wqSkU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 116}, "_enabled": true, "__prefab": {"__id__": 124}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 195.25, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "9cY/D3EolKOadAOrJrvWNM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "94UOW1xFtBFY03xqcyi+CE"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 115}, "_enabled": true, "__prefab": {"__id__": 127}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "5auDvQBy5BCbZf0GfGMNU7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 115}, "_enabled": true, "__prefab": {"__id__": 129}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 312.4, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "15yLQuHGJIGJuwNoEyzjMB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c8Nn7JUzBGMLajDaeBP/6T"}, {"__type__": "cc.Node", "_name": "head", "_objFlags": 0, "_parent": {"__id__": 114}, "_children": [{"__id__": 132}], "_active": true, "_components": [{"__id__": 142}, {"__id__": 144}], "_prefab": {"__id__": 146}, "_lpos": {"__type__": "cc.Vec3", "x": -312.4, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 131}, "_children": [], "_active": true, "_components": [{"__id__": 133}, {"__id__": 135}, {"__id__": 137}, {"__id__": 139}], "_prefab": {"__id__": 141}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 132}, "_enabled": true, "__prefab": {"__id__": 134}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51wTchGdJCRJUfQH3EDctw"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 132}, "_enabled": true, "id": "sl37", "isUpperCase": true, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 136}}, {"__type__": "cc.CompPrefabInfo", "fileId": "064H5zaEBLcrTfEsDz51kK"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 132}, "_enabled": true, "__prefab": {"__id__": 138}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "61OAmGHT5CoIPTP7l4x3pD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 132}, "_enabled": true, "__prefab": {"__id__": 140}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 195.25, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "5aGvv/7VJOPpUN/5jNNDG1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bcufX4TmxCELXOlVUKcD+1"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 131}, "_enabled": true, "__prefab": {"__id__": 143}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "adZkDhInNH2LPNBTjXvAxO"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 131}, "_enabled": true, "__prefab": {"__id__": 145}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 312.4, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "40yKmiAx5JNpOrSXp05GMD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4eNGa+bGNIDps07XZwQDG5"}, {"__type__": "cc.Node", "_name": "head", "_objFlags": 0, "_parent": {"__id__": 114}, "_children": [{"__id__": 148}], "_active": true, "_components": [{"__id__": 158}, {"__id__": 160}], "_prefab": {"__id__": 162}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 147}, "_children": [], "_active": true, "_components": [{"__id__": 149}, {"__id__": 151}, {"__id__": 153}, {"__id__": 155}], "_prefab": {"__id__": 157}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "__prefab": {"__id__": 150}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "TỔNG ĐẶT", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "768bA+rbBECq3Auky3UpsT"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "id": "sl49", "isUpperCase": true, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 152}}, {"__type__": "cc.CompPrefabInfo", "fileId": "deOgBN2I1N+7qyqfOhMtfa"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "__prefab": {"__id__": 154}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "7c/PV2SGxPI7f58PH1Govx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "__prefab": {"__id__": 156}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 195.25, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "ea3qK5qnVCCZ3urQn3WBc8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a56y5MAL1KVZZWeLRY105m"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 147}, "_enabled": true, "__prefab": {"__id__": 159}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "77JnLR+FdIZLRivNvY3J5x"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 147}, "_enabled": true, "__prefab": {"__id__": 161}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 312.4, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "83J5P2a01FXpySTzjD/d5o"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "af0OGE62FIf4cbsZeJOBsX"}, {"__type__": "cc.Node", "_name": "head", "_objFlags": 0, "_parent": {"__id__": 114}, "_children": [{"__id__": 164}], "_active": true, "_components": [{"__id__": 174}, {"__id__": 176}], "_prefab": {"__id__": 178}, "_lpos": {"__type__": "cc.Vec3", "x": 312.4, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 163}, "_children": [], "_active": true, "_components": [{"__id__": 165}, {"__id__": 167}, {"__id__": 169}, {"__id__": 171}], "_prefab": {"__id__": 173}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 164}, "_enabled": true, "__prefab": {"__id__": 166}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dxPuL8nxORpatYt+yxgYU"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 164}, "_enabled": true, "id": "sl5", "isUpperCase": true, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 168}}, {"__type__": "cc.CompPrefabInfo", "fileId": "09O6YNbEFPNK0BuTHiE43F"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 164}, "_enabled": true, "__prefab": {"__id__": 170}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "3a1TeFIatCaLZi9UNCPNef"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 164}, "_enabled": true, "__prefab": {"__id__": 172}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 195.25, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6JbyyEBlMbpCokxgSyZ6C"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "41HsbP7txAqbJfdpzcyFya"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 163}, "_enabled": true, "__prefab": {"__id__": 175}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "2cif5UktRA3LUP3NJpfZpo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 163}, "_enabled": true, "__prefab": {"__id__": 177}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 312.4, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5IGTiUPJHyqhxozLCiNsr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "48Dafuqn9MCaBjlgaLjuN8"}, {"__type__": "cc.Node", "_name": "head", "_objFlags": 0, "_parent": {"__id__": 114}, "_children": [{"__id__": 180}], "_active": true, "_components": [{"__id__": 190}, {"__id__": 192}], "_prefab": {"__id__": 194}, "_lpos": {"__type__": "cc.Vec3", "x": 624.8, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 179}, "_children": [], "_active": true, "_components": [{"__id__": 181}, {"__id__": 183}, {"__id__": 185}, {"__id__": 187}], "_prefab": {"__id__": 189}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 180}, "_enabled": true, "__prefab": {"__id__": 182}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON> ti<PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74bqREFrRKi6A2PpPMpHgZ"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 180}, "_enabled": true, "id": "sl43", "isUpperCase": true, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 184}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0mvwZbM5Mrbv0NGhYVmdk"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 180}, "_enabled": true, "__prefab": {"__id__": 186}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "01raFeAmVMH79YrY4jOg8F"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 180}, "_enabled": true, "__prefab": {"__id__": 188}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 195.25, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "be7SAz6u5IS5gxFy7FHGIb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "04q1vI1vpMW7kjl2GTkUtJ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 179}, "_enabled": true, "__prefab": {"__id__": 191}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "dejNutO35H3ZDL4WqyMVB3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 179}, "_enabled": true, "__prefab": {"__id__": 193}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 312.4, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdHCs12O1PPomUD7Eao7AJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d5qU3bS0JNZbkFTVbNABtZ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "__prefab": {"__id__": 196}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "97a5d1a3-2efa-4384-bdf5-de3dc47d264a@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "89S/l65J9HA6xBwmj2w7yC"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "__prefab": {"__id__": 198}, "_resizeMode": 2, "_N$layoutType": 0, "_N$padding": 0, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_affectedByScale": false, "_layoutSize": {"__type__": "cc.Size", "width": 1562, "height": 65}, "_layoutType": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bl0YED2NCtY2m8IBNvQLc"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "__prefab": {"__id__": 200}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cLdpe1qNNVreXXITgj2Am"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "__prefab": {"__id__": 202}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1562, "height": 65}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "0d/4TDFVBAa5Dvx88EQOpA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "56JBM7PmZCtodyOIY3XqAw"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [{"__id__": 205}, {"__id__": 273}], "_active": true, "_components": [{"__id__": 355}, {"__id__": 358}, {"__id__": 360}], "_prefab": {"__id__": 362}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -133, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 204}, "_children": [{"__id__": 206}], "_active": true, "_components": [{"__id__": 264}, {"__id__": 266}, {"__id__": 268}, {"__id__": 270}], "_prefab": {"__id__": 272}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 30.42149999999998, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 205}, "_children": [{"__id__": 207}], "_active": true, "_components": [{"__id__": 255}, {"__id__": 257}, {"__id__": 259}, {"__id__": 261}], "_prefab": {"__id__": 263}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 349, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "row_template", "_objFlags": 0, "_parent": {"__id__": 206}, "_children": [{"__id__": 208}, {"__id__": 216}, {"__id__": 224}, {"__id__": 232}, {"__id__": 240}], "_active": false, "_components": [{"__id__": 248}, {"__id__": 250}, {"__id__": 252}], "_prefab": {"__id__": 254}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -34, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 207}, "_children": [], "_active": true, "_components": [{"__id__": 209}, {"__id__": 211}, {"__id__": 213}], "_prefab": {"__id__": 215}, "_lpos": {"__type__": "cc.Vec3", "x": -624.8, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 210}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON> gian", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fEp61a41LCLzBawIwXb1u"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 212}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "6brlJmu/dO0bXkxCrf6fxu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 214}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 125.01, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "fctkiu2jJGC79NDechakoT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "93O/i5OAtGybGq5nAGOMan"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 207}, "_children": [], "_active": true, "_components": [{"__id__": 217}, {"__id__": 219}, {"__id__": 221}], "_prefab": {"__id__": 223}, "_lpos": {"__type__": "cc.Vec3", "x": -312.4, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "__prefab": {"__id__": 218}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "sds", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21rlEqBmhBAoGDY62yAVtq"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "__prefab": {"__id__": 220}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "36f1LGKRhAmL3oUKXAdM+5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "__prefab": {"__id__": 222}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2mniFA+ZIeoH60bba+aFt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "39TODd2VlAYpiTIAtMe+RP"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 207}, "_children": [], "_active": true, "_components": [{"__id__": 225}, {"__id__": 227}, {"__id__": 229}], "_prefab": {"__id__": 231}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 224}, "_enabled": true, "__prefab": {"__id__": 226}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 254, "g": 254, "b": 0, "a": 255}, "_useOriginalSize": true, "_string": "Phỏng", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7cz38ju/hJaqmXFYwWMxMU"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 224}, "_enabled": true, "__prefab": {"__id__": 228}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "79k7VPt5VI+5AZ0rvAkQ99"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 224}, "_enabled": true, "__prefab": {"__id__": 230}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 195.25, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0rYMBWexMCbvINSlyxztR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "976AsiiiZI75Fl/ZLTTWv/"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 207}, "_children": [], "_active": true, "_components": [{"__id__": 233}, {"__id__": 235}, {"__id__": 237}], "_prefab": {"__id__": 239}, "_lpos": {"__type__": "cc.Vec3", "x": 312.4, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 232}, "_enabled": true, "__prefab": {"__id__": 234}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 254, "g": 254, "b": 0, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7ayhS33gFGJYXwnKYZxim8"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 232}, "_enabled": true, "__prefab": {"__id__": 236}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "47s+xY2WNK7pw9YNmWZjYg"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 232}, "_enabled": true, "__prefab": {"__id__": 238}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 195.25, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2p7z3PYJCyqa/JYaWVcgQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0fZ0MrVoRNor8J/CdknPNl"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 207}, "_children": [], "_active": true, "_components": [{"__id__": 241}, {"__id__": 243}, {"__id__": 245}], "_prefab": {"__id__": 247}, "_lpos": {"__type__": "cc.Vec3", "x": 624.8, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 240}, "_enabled": true, "__prefab": {"__id__": 242}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "305YaxEBREwIzqPv4LyxP7"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 240}, "_enabled": true, "__prefab": {"__id__": 244}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "65kk/+IylK3KD+IXVoQtbZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 240}, "_enabled": true, "__prefab": {"__id__": 246}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 195.25, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "80aqrATvZE47N/WNB/5458"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1f23QPPudKH5YoqVwv2Bnu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 207}, "_enabled": true, "__prefab": {"__id__": 249}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cb8cf33d-4479-43d0-bb17-5e673a983dc1@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdbA5n6FlEXJNn+n9V17Cv"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 207}, "_enabled": true, "__prefab": {"__id__": 251}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "06QM/LQa1BM5+kNbK2IYSE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 207}, "_enabled": true, "__prefab": {"__id__": 253}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1562, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "15kcFlpztOPqAv2+wGDC8Q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0fYzF0xzlErZiAwI9M8oYU"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 206}, "_enabled": true, "__prefab": {"__id__": 256}, "_resizeMode": 1, "_N$layoutType": 0, "_N$padding": 0, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 2, "_verticalDirection": 1, "_horizontalDirection": 0, "_affectedByScale": false, "_layoutSize": {"__type__": "cc.Size", "width": 1562, "height": -2}, "_layoutType": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39lYIIdd9EgYFLawBuVP5/"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 206}, "_enabled": true, "__prefab": {"__id__": 258}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29ulyZ8IJApYMbMTkL6u2z"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 206}, "_enabled": true, "__prefab": {"__id__": 260}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6Io5TvAlIxLDAhguwMEJo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 206}, "_enabled": true, "__prefab": {"__id__": 262}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1562, "height": -2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}}, {"__type__": "cc.CompPrefabInfo", "fileId": "74ciRt/bhCmZxHKrbIpNFF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5aSnurYNZPCqaEYt07BkzU"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 205}, "_enabled": true, "__prefab": {"__id__": 265}, "_materials": [], "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "24APiNwthNeqANzrxr326d"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 205}, "_enabled": true, "__prefab": {"__id__": 267}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3b48X+cMNNwK2RBw8I4jbM"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 205}, "_enabled": true, "__prefab": {"__id__": 269}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "5a7cC5nsxGYa4hA71ZMDpS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 205}, "_enabled": true, "__prefab": {"__id__": 271}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1562, "height": 698}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "feCd0YWX5PnLWaJvEvSrXo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "85JB3JPh9HVZIRJSeTNUFb"}, {"__type__": "cc.Node", "_name": "Pagination", "_objFlags": 0, "_parent": {"__id__": 204}, "_children": [{"__id__": 274}, {"__id__": 292}, {"__id__": 326}], "_active": true, "_components": [{"__id__": 344}, {"__id__": 346}, {"__id__": 348}, {"__id__": 350}, {"__id__": 352}], "_prefab": {"__id__": 354}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -356.4215, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "prev_button", "_objFlags": 0, "_parent": {"__id__": 273}, "_children": [{"__id__": 275}], "_active": true, "_components": [{"__id__": 285}, {"__id__": 287}, {"__id__": 289}], "_prefab": {"__id__": 291}, "_lpos": {"__type__": "cc.Vec3", "x": -52, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "_parent": {"__id__": 274}, "_children": [], "_active": true, "_components": [{"__id__": 276}, {"__id__": 278}, {"__id__": 280}, {"__id__": 282}], "_prefab": {"__id__": 284}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 277}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c7d0ccbb-0861-49b9-94ad-f7b1d1be667a@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1fwjPj9oFN04jEiMIC8XQV"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 279}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "adPUu08eNB2pYm/Z+Y2xLg"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 281}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "acPX2zsgxLd6d9Z9s0/6GR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 283}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dLwE4sT5NopU4DoBipk0r"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f1StgVfK5PMImVkLCSJ53R"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 274}, "_enabled": true, "__prefab": {"__id__": 286}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941"}, "_duration": 0.1, "_zoomScale": 0.95, "_target": {"__id__": 275}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b89Sw8d+1PDbi7luEMMfkE"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 274}, "_enabled": true, "__prefab": {"__id__": 288}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "adItdfDWVHy7K2azuhPdeh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 274}, "_enabled": true, "__prefab": {"__id__": 290}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bNxqY85dHF4OClPJhD5gp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c5wJgtgtBOE6FKJLmZdABF"}, {"__type__": "cc.Node", "_name": "page_layout", "_objFlags": 0, "_parent": {"__id__": 273}, "_children": [{"__id__": 293}], "_active": true, "_components": [{"__id__": 319}, {"__id__": 321}, {"__id__": 323}], "_prefab": {"__id__": 325}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "button_page", "_objFlags": 0, "_parent": {"__id__": 292}, "_children": [{"__id__": 294}], "_active": true, "_components": [{"__id__": 312}, {"__id__": 314}, {"__id__": 316}], "_prefab": {"__id__": 318}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "_parent": {"__id__": 293}, "_children": [{"__id__": 295}], "_active": true, "_components": [{"__id__": 303}, {"__id__": 305}, {"__id__": 307}, {"__id__": 309}], "_prefab": {"__id__": 311}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 294}, "_children": [], "_active": true, "_components": [{"__id__": 296}, {"__id__": 298}, {"__id__": 300}], "_prefab": {"__id__": 302}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 295}, "_enabled": true, "__prefab": {"__id__": 297}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": false, "_font": {"__uuid__": "68937445-24df-4546-8665-f77de532cdab"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6qYgYs0ZFU5cGdCMEfD4l"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 295}, "_enabled": true, "__prefab": {"__id__": 299}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1e4KrfChPYJcs3pzYNh46"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 295}, "_enabled": true, "__prefab": {"__id__": 301}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 17.21, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "0ePMHf86pNJbNec7rIBslF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 293}, "asset": {"__uuid__": "b05bbf1d-5e6f-4442-9f81-bb081a55516a"}, "fileId": "71Jol/O95J5JTSrEJPxjdc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 294}, "_enabled": true, "__prefab": {"__id__": 304}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d04be3e1-7af3-4624-99e2-6181e266f020@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "70euPM8LhFxL/qZmHEFZh5"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 294}, "_enabled": true, "__prefab": {"__id__": 306}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8t4qU8RBAg7wru7tEyNE3"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 294}, "_enabled": true, "__prefab": {"__id__": 308}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fsePZyjxM14QkrfPnbsSy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 294}, "_enabled": true, "__prefab": {"__id__": 310}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bROwhGOpFK4faMQBu19c0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 293}, "asset": {"__uuid__": "b05bbf1d-5e6f-4442-9f81-bb081a55516a"}, "fileId": "e3uCJZE2hCKI57d04iv/3d"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 293}, "_enabled": true, "__prefab": {"__id__": 313}, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_normalSprite": {"__uuid__": "d04be3e1-7af3-4624-99e2-6181e266f020@f9941"}, "_hoverSprite": {"__uuid__": "a8bd29d6-d91e-41e9-afca-1854acea0657@f9941"}, "_pressedSprite": {"__uuid__": "a8bd29d6-d91e-41e9-afca-1854acea0657@f9941"}, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.95, "_target": null, "pressedSprite": {"__uuid__": "ac1555e8-59f8-440d-b09a-d60fccc30fc0"}, "hoverSprite": {"__uuid__": "ac1555e8-59f8-440d-b09a-d60fccc30fc0"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0k/jqtkFKGJW1yE0DOZtS"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 293}, "_enabled": true, "__prefab": {"__id__": 315}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "41DQW6Jd1JqIOUsT1Zkl1w"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 293}, "_enabled": true, "__prefab": {"__id__": 317}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "60bnvr+KBIG68OHIsbJKMx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 293}, "asset": {"__uuid__": "b05bbf1d-5e6f-4442-9f81-bb081a55516a"}, "fileId": "6coNsIKKdCAaPfCDCvm8vy"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 292}, "_enabled": true, "__prefab": {"__id__": 320}, "_resizeMode": 1, "_N$layoutType": 0, "_N$padding": 0, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 2, "_paddingRight": 2, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 1, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_affectedByScale": false, "_layoutSize": {"__type__": "cc.Size", "width": 54, "height": 50}, "_layoutType": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dq25Hk/xLSbkMFoXNW1yN"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 292}, "_enabled": true, "__prefab": {"__id__": 322}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "02sBJxIWlPyaSoxwBWgu5N"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 292}, "_enabled": true, "__prefab": {"__id__": 324}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "66buWc2tlAUYmdOOmMdHZ3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6e1eqHcN9AdagcTZFuD8Sf"}, {"__type__": "cc.Node", "_name": "next_button", "_objFlags": 0, "_parent": {"__id__": 273}, "_children": [{"__id__": 327}], "_active": true, "_components": [{"__id__": 337}, {"__id__": 339}, {"__id__": 341}], "_prefab": {"__id__": 343}, "_lpos": {"__type__": "cc.Vec3", "x": 52, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "_parent": {"__id__": 326}, "_children": [], "_active": true, "_components": [{"__id__": 328}, {"__id__": 330}, {"__id__": 332}, {"__id__": 334}], "_prefab": {"__id__": 336}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 327}, "_enabled": true, "__prefab": {"__id__": 329}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c5b84bbf-52ce-4d59-94c4-f761d7acd1bc@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58YouMwg5MOIZQURxKhc44"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 327}, "_enabled": true, "__prefab": {"__id__": 331}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0Yn8DLWlL2K6Hn0R3UqYn"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 327}, "_enabled": true, "__prefab": {"__id__": 333}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "daL6q82SdHFolkEoZ8k6Gf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 327}, "_enabled": true, "__prefab": {"__id__": 335}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "85r/hBmFZEmK2pd1+uLfXl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f5cWaqAllAlKvRg6x2SZnO"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 326}, "_enabled": true, "__prefab": {"__id__": 338}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941"}, "_duration": 0.1, "_zoomScale": 0.95, "_target": {"__id__": 327}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dOz9mc9hIf6GsDOOfRO6I"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 326}, "_enabled": true, "__prefab": {"__id__": 340}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "ebTOq8E+RMSY0sQ3dJPPst"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 326}, "_enabled": true, "__prefab": {"__id__": 342}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "e89TPGl01IkJfM7z434miJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1fQTDXL3NK8KcoZMDcDkaB"}, {"__type__": "24287umPrZDVpwH4BCgGTi6", "_name": "", "_objFlags": 0, "node": {"__id__": 273}, "_enabled": true, "backButton": {"__id__": 285}, "nextButton": {"__id__": 337}, "pageButtonsContainer": {"__id__": 292}, "pageButtonPrefab": {"__uuid__": "e379348c-e3ef-4740-82cc-445ea71945a3"}, "activePageButtonSprite": {"__uuid__": "a8bd29d6-d91e-41e9-afca-1854acea0657@f9941"}, "inactivePageButtonSprite": {"__uuid__": "d04be3e1-7af3-4624-99e2-6181e266f020@f9941"}, "_id": "", "__prefab": {"__id__": 345}}, {"__type__": "cc.CompPrefabInfo", "fileId": "7882GBDNJG2aBJdfdTxPV+"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 273}, "_enabled": true, "__prefab": {"__id__": 347}, "_resizeMode": 1, "_N$layoutType": 0, "_N$padding": 0, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_affectedByScale": false, "_layoutSize": {"__type__": "cc.Size", "width": 154, "height": 50}, "_layoutType": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7lNAcTehEw4vyHDQRFEXf"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 273}, "_enabled": true, "__prefab": {"__id__": 349}, "_alignFlags": 20, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": -2, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4Bkafz1BHEapTVBAAiMBC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 273}, "_enabled": true, "__prefab": {"__id__": 351}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "10NyYzpaxM9qzHRo6U95+I"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 273}, "_enabled": true, "__prefab": {"__id__": 353}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 154, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fy6bE1A9HNaWB9JB9Im7V"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7bDebemwVPUpdgNiT9R4wA"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 204}, "_enabled": true, "__prefab": {"__id__": 356}, "bounceDuration": 0.23, "brake": 0.75, "elastic": true, "inertia": true, "horizontal": false, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [{"__id__": 357}], "_content": {"__id__": 206}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0cHyE6TK5Ei7fXbT70Z4mf"}, {"__type__": "cc.ClickEvent", "target": null, "component": "", "_componentId": "cc481gP9AlCILXU1JsBrs7F", "handler": "scrollEvent", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 204}, "_enabled": true, "__prefab": {"__id__": 359}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "aa+tEX/elJ176wkrof1R5h"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 204}, "_enabled": true, "__prefab": {"__id__": 361}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1562, "height": 758.843}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "2e9Q14T5xNoJRCCY51Y4TK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "95arlUVdtMCpGDkNEsJW6D"}, {"__type__": "cc.Node", "_name": "<PERSON>", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [{"__id__": 364}, {"__id__": 432}], "_active": false, "_components": [{"__id__": 514}, {"__id__": 517}, {"__id__": 519}], "_prefab": {"__id__": 521}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -133, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 363}, "_children": [{"__id__": 365}], "_active": true, "_components": [{"__id__": 423}, {"__id__": 425}, {"__id__": 427}, {"__id__": 429}], "_prefab": {"__id__": 431}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 30.42149999999998, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 364}, "_children": [{"__id__": 366}], "_active": true, "_components": [{"__id__": 414}, {"__id__": 416}, {"__id__": 418}, {"__id__": 420}], "_prefab": {"__id__": 422}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 349, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "row_template", "_objFlags": 0, "_parent": {"__id__": 365}, "_children": [{"__id__": 367}, {"__id__": 375}, {"__id__": 383}, {"__id__": 391}, {"__id__": 399}], "_active": false, "_components": [{"__id__": 407}, {"__id__": 409}, {"__id__": 411}], "_prefab": {"__id__": 413}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -34, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 366}, "_children": [], "_active": true, "_components": [{"__id__": 368}, {"__id__": 370}, {"__id__": 372}], "_prefab": {"__id__": 374}, "_lpos": {"__type__": "cc.Vec3", "x": -624.8, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 367}, "_enabled": true, "__prefab": {"__id__": 369}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON> gian", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4sgu0Dy5GhJukYKfa9N5G"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 367}, "_enabled": true, "__prefab": {"__id__": 371}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "50+J6iwUxNQrfuzmZQtAdL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 367}, "_enabled": true, "__prefab": {"__id__": 373}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 125.01, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "29Rpze6aVBnb3+w1PBFHHU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3dAD1t+gxMu7zzgamIcacD"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 366}, "_children": [], "_active": true, "_components": [{"__id__": 376}, {"__id__": 378}, {"__id__": 380}], "_prefab": {"__id__": 382}, "_lpos": {"__type__": "cc.Vec3", "x": -312.4, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 375}, "_enabled": true, "__prefab": {"__id__": 377}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "Phỏng", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eb0Vv8jhpFE44t5oQvdApS"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 375}, "_enabled": true, "__prefab": {"__id__": 379}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "edFTSMDqlC6J46cm2oB19V"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 375}, "_enabled": true, "__prefab": {"__id__": 381}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5SRuBE5VPd4/+i7FjK0WH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "88KHoW4uRFvbC1PrOmC6dK"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 366}, "_children": [], "_active": true, "_components": [{"__id__": 384}, {"__id__": 386}, {"__id__": 388}], "_prefab": {"__id__": 390}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 383}, "_enabled": true, "__prefab": {"__id__": 385}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "Phỏng", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50HVrOeilHWrFs9OJY7k34"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 383}, "_enabled": true, "__prefab": {"__id__": 387}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "52qLeqLZBAr7NGTLtKWn/W"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 383}, "_enabled": true, "__prefab": {"__id__": 389}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 195.25, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6lFo9rqtHur7anZXHu0Z9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "08Gq4vKW1FfqLABLnGeHuo"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 366}, "_children": [], "_active": true, "_components": [{"__id__": 392}, {"__id__": 394}, {"__id__": 396}], "_prefab": {"__id__": 398}, "_lpos": {"__type__": "cc.Vec3", "x": 312.4, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 391}, "_enabled": true, "__prefab": {"__id__": 393}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a88a1KA5hF6Ja0LZdJDE3m"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 391}, "_enabled": true, "__prefab": {"__id__": 395}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "05QfZaQN5ItI4VyGLm4qhF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 391}, "_enabled": true, "__prefab": {"__id__": 397}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 195.25, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8UYRsMjFMKr6fR9nchw3g"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c8xJrXRcBPPIzZh6f8sqEl"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 366}, "_children": [], "_active": true, "_components": [{"__id__": 400}, {"__id__": 402}, {"__id__": 404}], "_prefab": {"__id__": 406}, "_lpos": {"__type__": "cc.Vec3", "x": 624.8, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 399}, "_enabled": true, "__prefab": {"__id__": 401}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "04Eei2xNlPqLXLHaaqOzKj"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 399}, "_enabled": true, "__prefab": {"__id__": 403}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "daU0tQ5BxFY5TimwDC/fkB"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 399}, "_enabled": true, "__prefab": {"__id__": 405}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 195.25, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9vm19UZJHLrO/07oNB9d4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b43VUnGoJHxKHEk0VCzxYp"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 366}, "_enabled": true, "__prefab": {"__id__": 408}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cb8cf33d-4479-43d0-bb17-5e673a983dc1@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2A1WNC/9DgbZBdcvYGe9t"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 366}, "_enabled": true, "__prefab": {"__id__": 410}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "55+lpch/tI5KKmwENwxtGA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 366}, "_enabled": true, "__prefab": {"__id__": 412}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1562, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3h+h/mVlDOKvnGuhC7iee"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e96iUZRIJFq69/z8UDe9mr"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 365}, "_enabled": true, "__prefab": {"__id__": 415}, "_resizeMode": 1, "_N$layoutType": 0, "_N$padding": 0, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 2, "_verticalDirection": 1, "_horizontalDirection": 0, "_affectedByScale": false, "_layoutSize": {"__type__": "cc.Size", "width": 1562, "height": -2}, "_layoutType": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dEDODYpFJh4cJVQHX4WCY"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 365}, "_enabled": true, "__prefab": {"__id__": 417}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17UtFqB11A9JS9k60TejHK"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 365}, "_enabled": true, "__prefab": {"__id__": 419}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "00QW1w9mpJY7y7rGqA9e5s"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 365}, "_enabled": true, "__prefab": {"__id__": 421}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1562, "height": -2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}}, {"__type__": "cc.CompPrefabInfo", "fileId": "12CQC7kW5FwLmx4+st3xeI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e5i6hJPJlEzIVz7ictXJ55"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 364}, "_enabled": true, "__prefab": {"__id__": 424}, "_materials": [], "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "152bipb4hNNbI3QgdDQAOP"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 364}, "_enabled": true, "__prefab": {"__id__": 426}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21Wjh14HhAcrqZv+pvU9FQ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 364}, "_enabled": true, "__prefab": {"__id__": 428}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4KJqoQG1JWZM/M7YGNzy6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 364}, "_enabled": true, "__prefab": {"__id__": 430}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1562, "height": 698}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "15PGhI88xCB4RuVLhC8O10"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "66gwxc3dZLeq8h+QATz0gV"}, {"__type__": "cc.Node", "_name": "Pagination", "_objFlags": 0, "_parent": {"__id__": 363}, "_children": [{"__id__": 433}, {"__id__": 451}, {"__id__": 485}], "_active": true, "_components": [{"__id__": 503}, {"__id__": 505}, {"__id__": 507}, {"__id__": 509}, {"__id__": 511}], "_prefab": {"__id__": 513}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -352.4215, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "prev_button", "_objFlags": 0, "_parent": {"__id__": 432}, "_children": [{"__id__": 434}], "_active": true, "_components": [{"__id__": 444}, {"__id__": 446}, {"__id__": 448}], "_prefab": {"__id__": 450}, "_lpos": {"__type__": "cc.Vec3", "x": -52, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "_parent": {"__id__": 433}, "_children": [], "_active": true, "_components": [{"__id__": 435}, {"__id__": 437}, {"__id__": 439}, {"__id__": 441}], "_prefab": {"__id__": 443}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 434}, "_enabled": true, "__prefab": {"__id__": 436}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c7d0ccbb-0861-49b9-94ad-f7b1d1be667a@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "banNuaWERBLrLerv4EWx6b"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 434}, "_enabled": true, "__prefab": {"__id__": 438}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c4agCqB85JxZLg2g+h+llE"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 434}, "_enabled": true, "__prefab": {"__id__": 440}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "8bvHNiuu1MJ70pWKCDll8O"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 434}, "_enabled": true, "__prefab": {"__id__": 442}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "4dcU1AABpNCatIVrP1tJAf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "11BI7cKuJOZLHKpg9ODof6"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 433}, "_enabled": true, "__prefab": {"__id__": 445}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941"}, "_duration": 0.1, "_zoomScale": 0.95, "_target": {"__id__": 434}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55G1p3J3xNyr0K0znBASgn"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 433}, "_enabled": true, "__prefab": {"__id__": 447}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0HcsOCUdB9J8G0COZlyNd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 433}, "_enabled": true, "__prefab": {"__id__": 449}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "24LMpuCgVNYpmT7+ooYufs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "24cddLE6FHk5gQXwQscYsd"}, {"__type__": "cc.Node", "_name": "page_layout", "_objFlags": 0, "_parent": {"__id__": 432}, "_children": [{"__id__": 452}], "_active": true, "_components": [{"__id__": 478}, {"__id__": 480}, {"__id__": 482}], "_prefab": {"__id__": 484}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "button_page", "_objFlags": 0, "_parent": {"__id__": 451}, "_children": [{"__id__": 453}], "_active": true, "_components": [{"__id__": 471}, {"__id__": 473}, {"__id__": 475}], "_prefab": {"__id__": 477}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "_parent": {"__id__": 452}, "_children": [{"__id__": 454}], "_active": true, "_components": [{"__id__": 462}, {"__id__": 464}, {"__id__": 466}, {"__id__": 468}], "_prefab": {"__id__": 470}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 453}, "_children": [], "_active": true, "_components": [{"__id__": 455}, {"__id__": 457}, {"__id__": 459}], "_prefab": {"__id__": 461}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 454}, "_enabled": true, "__prefab": {"__id__": 456}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": false, "_font": {"__uuid__": "68937445-24df-4546-8665-f77de532cdab"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9aULgwpWxBx5hs4Ra2oL0K"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 454}, "_enabled": true, "__prefab": {"__id__": 458}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "37rLlhbB5P8LWzBYGNay7K"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 454}, "_enabled": true, "__prefab": {"__id__": 460}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 17.21, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "04FFAyJSBJr4NfhiDGR860"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 452}, "asset": {"__uuid__": "b05bbf1d-5e6f-4442-9f81-bb081a55516a"}, "fileId": "71Jol/O95J5JTSrEJPxjdc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 453}, "_enabled": true, "__prefab": {"__id__": 463}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d04be3e1-7af3-4624-99e2-6181e266f020@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "13oC5kQcVEzLqnp5+c+yJ9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 453}, "_enabled": true, "__prefab": {"__id__": 465}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eJOAW4jtA+onLA/mdFcQJ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 453}, "_enabled": true, "__prefab": {"__id__": 467}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "5139568zxPfIaOsBHMKpnn"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 453}, "_enabled": true, "__prefab": {"__id__": 469}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "771Gcw3e9CDKJartO9Oj9J"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 452}, "asset": {"__uuid__": "b05bbf1d-5e6f-4442-9f81-bb081a55516a"}, "fileId": "e3uCJZE2hCKI57d04iv/3d"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 452}, "_enabled": true, "__prefab": {"__id__": 472}, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_normalSprite": {"__uuid__": "d04be3e1-7af3-4624-99e2-6181e266f020@f9941"}, "_hoverSprite": {"__uuid__": "a8bd29d6-d91e-41e9-afca-1854acea0657@f9941"}, "_pressedSprite": {"__uuid__": "a8bd29d6-d91e-41e9-afca-1854acea0657@f9941"}, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.95, "_target": null, "pressedSprite": {"__uuid__": "ac1555e8-59f8-440d-b09a-d60fccc30fc0"}, "hoverSprite": {"__uuid__": "ac1555e8-59f8-440d-b09a-d60fccc30fc0"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90v1Kv4YJEHrCrsd2TU3H9"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 452}, "_enabled": true, "__prefab": {"__id__": 474}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "634OTfIx5NyLPmSdZmDtyV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 452}, "_enabled": true, "__prefab": {"__id__": 476}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "17t+Acvj1OQbcWVMVXUahu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 452}, "asset": {"__uuid__": "b05bbf1d-5e6f-4442-9f81-bb081a55516a"}, "fileId": "6coNsIKKdCAaPfCDCvm8vy"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 451}, "_enabled": true, "__prefab": {"__id__": 479}, "_resizeMode": 1, "_N$layoutType": 0, "_N$padding": 0, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 2, "_paddingRight": 2, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 1, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_affectedByScale": false, "_layoutSize": {"__type__": "cc.Size", "width": 54, "height": 50}, "_layoutType": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "00sw0csmBHPY+3dxWnV9p4"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 451}, "_enabled": true, "__prefab": {"__id__": 481}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfqym/tXtKqYlyvFMMvTxu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 451}, "_enabled": true, "__prefab": {"__id__": 483}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "05AIgng6FJ+bz4GhVGuC5J"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "acUMhRAA1H/odxAKMVRK+W"}, {"__type__": "cc.Node", "_name": "next_button", "_objFlags": 0, "_parent": {"__id__": 432}, "_children": [{"__id__": 486}], "_active": true, "_components": [{"__id__": 496}, {"__id__": 498}, {"__id__": 500}], "_prefab": {"__id__": 502}, "_lpos": {"__type__": "cc.Vec3", "x": 52, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "_parent": {"__id__": 485}, "_children": [], "_active": true, "_components": [{"__id__": 487}, {"__id__": 489}, {"__id__": 491}, {"__id__": 493}], "_prefab": {"__id__": 495}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 486}, "_enabled": true, "__prefab": {"__id__": 488}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c5b84bbf-52ce-4d59-94c4-f761d7acd1bc@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58lJ8JPLxM3YnCrjUJR2nl"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 486}, "_enabled": true, "__prefab": {"__id__": 490}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bL0jHLjhOyKRWnMOV4MXC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 486}, "_enabled": true, "__prefab": {"__id__": 492}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "4a8aNjsX5DRYRdDxwS64as"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 486}, "_enabled": true, "__prefab": {"__id__": 494}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "18Bzsz8WtI76vEevL3A3OW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0086xEga9PGap+QHvspnJf"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 485}, "_enabled": true, "__prefab": {"__id__": 497}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941"}, "_duration": 0.1, "_zoomScale": 0.95, "_target": {"__id__": 486}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8zbIEGvNOwpyY/WWk5HaP"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 485}, "_enabled": true, "__prefab": {"__id__": 499}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "48vaYoP2BEa4O6Odwfn+BP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 485}, "_enabled": true, "__prefab": {"__id__": 501}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fI+HjVW9MqYoXAKDVsjPX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d1r/AtFFlJ5qdxvlCJOfHj"}, {"__type__": "24287umPrZDVpwH4BCgGTi6", "_name": "", "_objFlags": 0, "node": {"__id__": 432}, "_enabled": true, "backButton": {"__id__": 444}, "nextButton": {"__id__": 496}, "pageButtonsContainer": {"__id__": 451}, "pageButtonPrefab": {"__uuid__": "e379348c-e3ef-4740-82cc-445ea71945a3"}, "activePageButtonSprite": {"__uuid__": "a8bd29d6-d91e-41e9-afca-1854acea0657@f9941"}, "inactivePageButtonSprite": {"__uuid__": "d04be3e1-7af3-4624-99e2-6181e266f020@f9941"}, "_id": "", "__prefab": {"__id__": 504}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4AVMx2JhMkbpeuHmq6eoC"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 432}, "_enabled": true, "__prefab": {"__id__": 506}, "_resizeMode": 1, "_N$layoutType": 0, "_N$padding": 0, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_affectedByScale": false, "_layoutSize": {"__type__": "cc.Size", "width": 154, "height": 50}, "_layoutType": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bd55Z8VeNN9pqDM90z2Xth"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 432}, "_enabled": true, "__prefab": {"__id__": 508}, "_alignFlags": 20, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 2, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43pN+OYc1MJ75vEKRJ+GXf"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 432}, "_enabled": true, "__prefab": {"__id__": 510}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eyEYPsEFDh4Km9hqDX3C6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 432}, "_enabled": true, "__prefab": {"__id__": 512}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 154, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "6czhaxEiVPcKmL/cWYbbL7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5dqGVn3FhG5bLIA3I1E+WS"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 515}, "bounceDuration": 0.23, "brake": 0.75, "elastic": true, "inertia": true, "horizontal": false, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [{"__id__": 516}], "_content": {"__id__": 365}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5f9Ppt4CZExZJIpbBMPHct"}, {"__type__": "cc.ClickEvent", "target": null, "component": "", "_componentId": "cc481gP9AlCILXU1JsBrs7F", "handler": "scrollEvent", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 518}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "49/qrCrFpGw6UZBhHVAZ0h"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 520}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1562, "height": 758.843}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "96x1WTPL5OI5xjDzdNfw46"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "86fFC0lThDEq1aR26BBBDT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 523}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dbdbf5df-99ca-4366-ade6-eecd436f94bc@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fe0uOkcmtFOopx+OV9Pfam"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 525}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5Or/OLoRAkrowZk05kP0C"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 527}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1660, "height": 1055}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bC5qkATFFco5+piQcOfqA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "96SghbGeJANLMn48LN1fIj"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 530}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "abBWuIG+tDcabIIo7QjaEh"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 532}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "545nmoXg9MVoJR+l/XKVvV"}, {"__type__": "0ae26IAIF5KZ6qO8QyYrJWa", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "bg": {"__id__": 2}, "container": {"__id__": 14}, "tabs": {"__id__": 107}, "tabsContent": [{"__id__": 204}, {"__id__": 363}], "contentTipzo": {"__id__": 206}, "rowTipzo": {"__id__": 207}, "paginationTipzo": {"__id__": 344}, "contentXu": {"__id__": 365}, "rowXu": {"__id__": 366}, "paginationXu": {"__id__": 503}, "rowBackground1": {"__uuid__": "cb8cf33d-4479-43d0-bb17-5e673a983dc1@f9941"}, "rowBackground2": {"__uuid__": "3ed6bc76-31be-44a2-bd20-57195812ca3e@f9941"}, "_id": "", "__prefab": {"__id__": 534}}, {"__type__": "cc.CompPrefabInfo", "fileId": "90TEfhC6NGzpo48ebG/C/S"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 536}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5Z1eWjIFKi4MrUFcuFtoI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 538}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b45tbh1elJK5elv67UmefB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8fCreP7YlDparA//NMoExR", "targetOverrides": [{"__id__": 540}, {"__id__": 543}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 293}, "sourceInfo": {"__id__": 541}, "propertyPath": ["_target"], "target": {"__id__": 293}, "targetInfo": {"__id__": 542}}, {"__type__": "cc.TargetInfo", "localID": ["8eYBYalq1EDKiwdzEXYY0Y"]}, {"__type__": "cc.TargetInfo", "localID": ["e3uCJZE2hCKI57d04iv/3d"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 452}, "sourceInfo": {"__id__": 544}, "propertyPath": ["_target"], "target": {"__id__": 452}, "targetInfo": {"__id__": 545}}, {"__type__": "cc.TargetInfo", "localID": ["8eYBYalq1EDKiwdzEXYY0Y"]}, {"__type__": "cc.TargetInfo", "localID": ["e3uCJZE2hCKI57d04iv/3d"]}]
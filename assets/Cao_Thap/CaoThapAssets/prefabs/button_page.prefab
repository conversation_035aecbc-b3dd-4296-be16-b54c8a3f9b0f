[{"__type__": "cc.Prefab", "_name": "button_page", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false, "persistent": false}, {"__type__": "cc.Node", "_name": "button_page", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 20}, {"__id__": 22}, {"__id__": 24}], "_prefab": {"__id__": 26}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_active": true, "_components": [{"__id__": 11}, {"__id__": 13}, {"__id__": 15}, {"__id__": 17}], "_prefab": {"__id__": 19}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": false, "_font": {"__uuid__": "68937445-24df-4546-8665-f77de532cdab"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86JX0nE+tGP6tiX1Us6e+P"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fOvO6gXBNDJRcSCz5BBX2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 9}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 17.21, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1eGcdz6r1DWYitE9oOINNk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b05bbf1d-5e6f-4442-9f81-bb081a55516a"}, "fileId": "71Jol/O95J5JTSrEJPxjdc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 12}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d04be3e1-7af3-4624-99e2-6181e266f020@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8yJu1yTZJZrmWRsGPgYUf"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 14}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50Ps4oAYNCgLyg7jm1Dxhm"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 16}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "1a3xyUv6xPSZsHwXDuK6go"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 18}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2uHHitjJMzZ11QGWb0oIJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b05bbf1d-5e6f-4442-9f81-bb081a55516a"}, "fileId": "e3uCJZE2hCKI57d04iv/3d"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 21}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_normalSprite": {"__uuid__": "d04be3e1-7af3-4624-99e2-6181e266f020@f9941"}, "_hoverSprite": {"__uuid__": "a8bd29d6-d91e-41e9-afca-1854acea0657@f9941"}, "_pressedSprite": {"__uuid__": "a8bd29d6-d91e-41e9-afca-1854acea0657@f9941"}, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.95, "_target": null, "pressedSprite": {"__uuid__": "ac1555e8-59f8-440d-b09a-d60fccc30fc0"}, "hoverSprite": {"__uuid__": "ac1555e8-59f8-440d-b09a-d60fccc30fc0"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86Yz2Iq3tKRIJET3dDH3zy"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 23}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cDDmius1BxbZq0kz/6KGL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 25}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6xdvD+bVMfY5yDryGiTbB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b05bbf1d-5e6f-4442-9f81-bb081a55516a"}, "fileId": "551Zy5lMBFDZ+PZHPCEbKp", "targetOverrides": [{"__id__": 27}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 1}, "sourceInfo": {"__id__": 28}, "propertyPath": ["_target"], "target": {"__id__": 1}, "targetInfo": {"__id__": 29}}, {"__type__": "cc.TargetInfo", "localID": ["8eYBYalq1EDKiwdzEXYY0Y"]}, {"__type__": "cc.TargetInfo", "localID": ["e3uCJZE2hCKI57d04iv/3d"]}]
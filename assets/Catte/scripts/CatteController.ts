import { _decorator, assetManager, Component, Label, Node, ParticleSystem2D, Sprite, SpriteFrame, Vec3, Animation, tween, instantiate } from "cc";
import { CattePlayerController } from "./CattePlayerController";
import App from "../../Lobby/scripts/common/App";
import Configs from "../../Lobby/scripts/common/Config";
import { CatteHUDController } from "./Catte.HUD";
import { loadBundleCatte } from "./Catte.Utils";
import CardGameSignalRClient from "../../Lobby/scripts/common/networks/CardGameSignalRClient";
import { Utils } from "../../Lobby/scripts/common/Utils";
import ChatInGame from "../../Lobby/scripts/common/ChatInGame";
import BundleControl from "../../Loading/scripts/BundleControl";
const { ccclass, property } = _decorator;

@ccclass("CatteController")
export class CatteController extends Component {
  public static Instance: CatteController;

  @property(CatteHUDController)
  HUD: CatteHUDController;

  @property(CattePlayerController)
  PlayerList: CattePlayerController[] = [];

  @property(Sprite)
  dealer: Sprite;

  @property(Node)
  WinPopUp: Node;

  @property(Node)
  Detail: Node;

  @property(Node)
  NoityPlayerCount: Node;

  @property(ParticleSystem2D)
  Effect: ParticleSystem2D;

  spriteFramesDe: SpriteFrame[] = [];
  spriteFramesNo: SpriteFrame[] = [];

  Player: CattePlayerController;
  chatIngame: ChatInGame = null;

  private cards: SpriteFrame[] = [];

  private currentPlayersList: any[] = [];
  private currentPlayerData: any;

  private playerCount: number = 0;
  private isQuitting: boolean = false;
  private session: number = 0;
  private countDown: number = 60;
  private _flagCheckOnline: boolean = false;
  private isPlaying: boolean = false;

  protected onLoad(): void {
    CatteController.Instance = this;
  }

  protected start(): void {
    loadBundleCatte(() => {
      var bundle = assetManager.getBundle("Catte");

      bundle.loadDir("res/Card/", SpriteFrame, (err, spriteFrames: SpriteFrame[]) => {
        if (err) {
          console.error("Failed to load atlas:", err);
          return;
        }
        this.cards = spriteFrames;
      });
    });
         this.playAnimationnormal();
    const data = App.instance.DataPass[0];
    this._flagCheckOnline = true;

    if (this.HUD) this.HUD.setInfoLabel(data, this.session);

    this.scheduleOnce(() => {
      this.playerJoin(App.instance.DataPass[0]);
    }, 1);

    if (this.Detail) {
      this.Detail.active = true;
      const label = this.Detail.getChildByName("Label").getComponent(Label);
      if (label) {
        label.string = App.instance.getTextLang("me6");
      }
    }

    CardGameSignalRClient.getInstance().receive("joinRoom", (data: any) => {
      if (data.r) {
        App.instance.showToast(App.instance.getTextLang("me6"));
        this.playerJoin(data.r);
      }
    });

    CardGameSignalRClient.getInstance().receive("roomData", (data: any) => {
      if (data.r) {
        this.onHandleRoom(data.r);
      }
    });

    CardGameSignalRClient.getInstance().receive("showCard", (data: any) => {
      if (data.r) {
        console.log("showCard", data.r);
      }
    });

    CardGameSignalRClient.getInstance().receive("registerLeavingRoom", (data: any) => {
      if (!data.r) return;
      this.countDown = 60;
      this.PlayerList.forEach((e) => {
        if (e.Data && e.Data.AccountId === data.r.id) {
          if (e.Data.AccountId != this.currentPlayerData.AccountId) {
            App.instance.showToast(App.instance.getTextLang("me11004").replace("{0}", e.Data.Nickname));
          } else {
            if (data && data.code == 11007) {
              var stringaler = App.instance.getTextLang("me11007");
              App.instance.ShowAlertDialog(Utils.formatString(stringaler, Utils.formatNumber(data.prms[0]), data.prms[0] == 1 ? App.instance.getTextLang("hi25") : App.instance.getTextLang("TLN_COIN")));
            }
            this.isQuitting = true;
          }
        }
      });
    });

    CardGameSignalRClient.getInstance().receiveArray("recieveMessage", (accountId: string, _nickname: string, content: string) => {
      this.PlayerList.forEach((e) => {
        if (e.Data && e.Data.AccountId === accountId) {
          e.showChatMsg(content);
        }
      });
    });
  }

  actChat() {
    this.countDown = 60;
    App.instance.inactivityTimer = 0;
    if (this.chatIngame == null) {
      let cb = (prefab) => {
        const chat: Node = instantiate(prefab);
        this.HUD.node.addChild(chat);

        this.chatIngame = chat.getComponent(ChatInGame);
        this.chatIngame.show(Configs.InGameIds.Catte);
      };
      BundleControl.loadPrefabPopup("prefabs/ChatInGame", cb);
    } else {
      this.chatIngame.show(Configs.InGameIds.Catte);
    }
  }

  backToLobby() {
    if (this.isQuitting) return;
    this.isQuitting = true;
    this.sendExit();
    if (!this.isPlaying) {
      App.instance.gotoLobby();
      Utils.setStorageValue("last_open_game_id", "");
    }
    if (this.playerCount <= 1) {
      App.instance.gotoLobby();
      Utils.setStorageValue("last_open_game_id", "");
    }
  }

  sendExit() {
    CardGameSignalRClient.getInstance().send("ExitRoom", [], null);
  }

  playAnimationDealer() {
    if (this.dealer) {
      const anim = this.dealer.getComponent(Animation);
      if (anim) {
        anim.stop();
        const stateName = "chia";
        const state = anim.getState(stateName);
        if (state) {
          anim.play(stateName);
        }
      }
    }
  }

  playAnimationnormal() {
    if (this.dealer) {
      const anim = this.dealer.getComponent(Animation);

      if (anim) {
        anim.stop();
        const stateName = "dealer";
        const state = anim.getState(stateName);
        if (state) {
          anim.play(stateName);
        }
      }
    }
  }

  getCardByName(cardNum: string): SpriteFrame {
    for (let spr of this.cards) {
      if (spr.name === cardNum) {
        return spr;
      }
    }
    return null;
  }

  playerJoin(data) {
    const newArr = [...data.Players.filter((item) => item.Nickname.split("[X]")[1] === Configs.Login.Nickname), ...data.Players.filter((item) => item.Nickname.split("[X]")[1] !== Configs.Login.Nickname)];
    this.currentPlayersList = newArr;
    this.updatePlayerList(this.currentPlayersList);
  }

  updatePlayerList(players: any[]) {
    if (this.NoityPlayerCount) {
      this.NoityPlayerCount.active = players.length <= 1;
      this.playerCount = players.length;
    }
    if (players && players.length) {
      this.PlayerList.forEach((e, i) => {
        if (players[i]) {
          if (players[i].Nickname.split("[X]")[1] === Configs.Login.Nickname) {
            this.Player = e;
            this.currentPlayerData = players[i];
            e.setInfo(players[i], true);
          } else {
            e.setInfo(players[i], false);
          }
        } else {
          e.setEmpty();
        }
      });
    }
  }

  onHandleRoom(data) {
    console.log("Update: ", data);
    const newArr = [...data.Players.filter((item) => item.Nickname.split("[X]")[1] === Configs.Login.Nickname), ...data.Players.filter((item) => item.Nickname.split("[X]")[1] !== Configs.Login.Nickname)];
    this.currentPlayersList = newArr;
    this.updatePlayerList(this.currentPlayersList);
    const session = data.Session;
    if (session) {
      if (session.Phrase === 1) {
        this.session++;
        this.isPlaying = true;
        this.HUD.setInfoLabel(data, this.session);
        if (this.Detail) {
          this.Detail.active = true;
        }
        this.playAnimationDealer();
        this.scheduleOnce(() => {
          this.playAnimationnormal();
        }, 1.5);
      } else if (session.Phrase === 4) {
        if (this.Detail) {
          this.Detail.active = false;
        }
        const specialWinData = this.hasFourOfAKind(this.currentPlayerData.Hand?.HandCards);
        this.PlayerList.forEach((e) => {
          if (e.Data) {
            if (e.Data.AccountId === session.Winner) {
              e.showCardEndGame(e.Data, true, session.Timeout, specialWinData.hasFour);
            } else {
              e.showCardEndGame(e.Data, false, session.Timeout);
            }
            if (session.Prizes && session.Prizes.length) {
              session.Prizes.forEach((prize) => {
                if (prize.id === e.Data.AccountId) {
                  e.showPrize(prize.prize);
                }
              });
            }
          }
        });
        if (session.Winner === this.currentPlayerData.AccountId) {
          this.showWinPopUp(specialWinData.hasFour, this.currentPlayerData.Hand?.HandCards, specialWinData.card);
          if (this.Effect) {
            this.Effect.node.active = true;
            this.Effect.resetSystem();
            this.scheduleOnce(() => {
              this.Effect.node.active = false;
              if (this.isQuitting) {
                App.instance.gotoLobby();
                Utils.setStorageValue("last_open_game_id", "");
              }
            }, 6);
          }
        }
        this.isPlaying = false;
      } else if (session.Phrase === 3) {
        if (this.Detail) {
          this.Detail.active = false;
        }
        this.PlayerList.forEach((e) => {
          if (e.Data) {
            e.stopTimeMiming();
            if (e.Data.AccountId === session.Role) {
              e.showTimming(session.Timeout);
            }
          }
        });
        if (this.HUD) {
          if (session.Role === this.currentPlayerData.AccountId) {
            this.HUD.activeGroupButton(true);
          } else {
            this.HUD.activeGroupButton(false);
          }
        }
      }
    }
  }

  getCardInfo(id: number) {
    if (id < 0 || id > 51) {
      return this.getCardByName("Flip");
    }

    const cardNumber = id % 13;
    const cardSuite = id - cardNumber;

    const cardNumberTextMap = ["2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K", "A"];
    const cardSuiteTextMap: { [key: number]: string } = {
      0: "club",
      13: "heart",
      26: "spade",
      39: "diamond",
    };

    const text = `${cardSuiteTextMap[cardSuite]}-${cardNumberTextMap[cardNumber]}`;
    return this.getCardByName(text);
  }

  hasFourOfAKind(cards: number[]): { card: number; hasFour: boolean } {
    const countMap: { [key: number]: number } = {};

    for (const id of cards) {
      if (id < 0 || id > 51) continue;

      const cardNumber = id % 13;

      if (!countMap[cardNumber]) {
        countMap[cardNumber] = 1;
      } else {
        countMap[cardNumber]++;
      }

      if (countMap[cardNumber] >= 4) {
        return { card: cardNumber, hasFour: true };
      }
    }

    return { card: null, hasFour: false };
  }

  showWinPopUp(isSpecialWin: boolean, cards: number[] = [], value?: number) {
    if (this.HUD) {
      this.HUD.showWinPopUp(isSpecialWin, cards, value);
    }
  }

  faceDownCard() {
    this.sendShowCard(true);
  }

  playCard() {
    this.sendShowCard(false);
  }

  sendShowCard(faceDown: boolean) {
    this.countDown = 60;
    if (this.Player.CurrentCardSelecting) {
      App.instance.showLoading(true);
      CardGameSignalRClient.getInstance().send("ShowCard", [this.Player.CurrentCardSelecting, faceDown], (data) => {
        App.instance.showLoading(false);
        if (data.r) {
          this.Player.stopTimeMiming();
        } else {
          App.instance.showToast(App.instance.getTextLang("ca-27000"));
        }
      });
    } else {
      App.instance.showToast(App.instance.getTextLang("ca-27000"));
    }
  }

  protected update(dt: number): void {
    if (this._flagCheckOnline) {
      this.countDown -= dt;
      if (this.countDown <= 0) {
        this._flagCheckOnline = false;
        App.instance.ShowAlertDialog(App.instance.getTextLang("ca247"));
        App.instance.gotoLobby();
        Utils.setStorageValue("last_open_game_id", "");
        this.sendExit();
      }
    }
  }
}

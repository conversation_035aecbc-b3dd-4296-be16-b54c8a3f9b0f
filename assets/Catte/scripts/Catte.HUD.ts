import { _decorator, Component, Label, Node, tween, UIOpacity, Vec3 } from "cc";
import App from "../../Lobby/scripts/common/App";
import { CardItemController } from "./CardItemController";
const { ccclass, property } = _decorator;

@ccclass("CatteHUDController")
export class CatteHUDController extends Component {
  @property(Label)
  InfoLabel: Label;

  @property(Node)
  BackgroundNormal: Node;

  @property(Node)
  BackgroundVip: Node;

  @property(Node)
  GroupButtons: Node;

  @property(Node)
  WinPopUp: Node;

  setInfoLabel(data: any, session: number) {
    const isVip = data.Currency;
    const price = data.Value;
    const currency = isVip ? "Tipzo" : "Coin";
    const name = data.Id;

    if (this.InfoLabel) {
      this.InfoLabel.string = `${isVip ? `${App.instance.getTextLang("tb112")}` : `${App.instance.getTextLang("tb113")}`} ${name ?? ""}\n${`${App.instance.getTextLang("iap38")}`}: ${price.toLocaleString("vi")} ${currency}\n${`${App.instance.getTextLang("ca95")}`}: #${session === 0 ? "" : session}`;
    }
    if (isVip) {
      if (this.BackgroundNormal) this.BackgroundNormal.active = false;
      if (this.BackgroundVip) this.BackgroundVip.active = true;
    } else {
      if (this.BackgroundNormal) this.BackgroundNormal.active = true;
      if (this.BackgroundVip) this.BackgroundVip.active = false;
    }
  }

  blurButton(isBlur: boolean) {
    if (this.GroupButtons) {
      const uo = this.GroupButtons.getComponent(UIOpacity);
      if (uo) {
        uo.opacity = isBlur ? 150 : 255;
      }
    }
  }

  activeGroupButton(active: boolean) {
    if (this.GroupButtons) {
      this.GroupButtons.active = active;
      if (active) {
        this.blurButton(true);
      }
    }
  }

  showWinPopUp(isSpecialWin: boolean, cards: number[] = [], value?: number) {
    if (this.WinPopUp) {
      this.WinPopUp.scale = Vec3.ZERO;
      const special = this.WinPopUp.children[0];
      if (special) {
        special.active = isSpecialWin;
        //Check has 4 card same kind
        const CardLayoutNode = special.children[0];
        if (cards && cards.length && CardLayoutNode) {
          for (let i = 0; i < 6; i++) {
            let cardNode = CardLayoutNode.children[i];
            if (cards[i]) {
              cardNode.active = true;
              let comp = cardNode.getComponent(CardItemController);
              if (comp) {
                comp.show(cards[i], cards[i] % 13 === value);
              }
            } else {
              cardNode.active = false;
            }
          }
        }
      }
      const win = this.WinPopUp.children[1];
      if (win) {
        win.active = !isSpecialWin;
      }
      tween(this.WinPopUp)
        .to(0.5, { scale: Vec3.ONE }, { easing: "elasticOut" })
        .delay(1)
        .call(() => {
          tween(this.WinPopUp).to(0.5, { scale: Vec3.ZERO }, { easing: "elasticOut" }).start();
        })
        .start();
    }
  }
}

import { _decorator, Button, Color, Component, Node, Sprite } from "cc";
import { CatteController } from "./CatteController";
const { ccclass, property } = _decorator;

@ccclass("CardItemController")
export class CardItemController extends Component {
  private cardId: number;
  private callBack: (id: number) => void;

  public get CardID(): number {
    return this.cardId;
  }

  protected onLoad(): void {
    this.node.addComponent(Button);
    this.node.on("click", this.onCardClicked, this);
  }

  protected onDestroy(): void {
    this.node.off("click", this.onCardClicked, this);
  }

  private onCardClicked(): void {
    CatteController.Instance.HUD.blurButton(false);
    if (this.cardId) {
      this.callBack && this.callBack(this.cardId);
    }
  }

  show(id: number, isDark: boolean = false, cb?: (id: number) => void) {
    this.cardId = id;
    let spr = this.getComponent(Sprite);
    if (spr) {
      spr.color = isDark ? new Color(255, 255, 255) : new Color(211, 211, 211);
      spr.spriteFrame = CatteController.Instance.getCardInfo(id);
    }
    if (cb) {
      this.callBack = cb;
    }
  }
}

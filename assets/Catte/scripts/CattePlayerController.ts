import { _decorator, assetManager, Color, Component, Label, Node, Sprite, SpriteFrame, tween, UIOpacity, Vec3 } from "cc";
import App from "../../Lobby/scripts/common/App";
import { CardItemController } from "./CardItemController";
import { LanguageManager } from "../../Lobby/scripts/common/language/Language.LanguageManager";
const { ccclass, property } = _decorator;

@ccclass("CattePlayerController")
export class CattePlayerController extends Component {
  Data: any;

  @property(Node)
  NonePlayer: Node;

  @property(Node)
  PlayerNode: Node;

  @property(Node)
  CardLayoutNode: Node;

  @property(Node)
  MyCard: Node;

  @property(Label)
  Name: Label;

  @property(Label)
  Amount: Label;

  @property(Sprite)
  spriteChat: Sprite;

  @property(Label)
  chatMsg: Label;

  @property(Label)
  CardCount: Label;

  @property(Sprite)
  private winLabel: Sprite;

  @property(Label)
  Price: Label;

  @property(Sprite)
  Cover: Sprite;

  @property(SpriteFrame)
  CoverSprites: SpriteFrame[] = [];

  private isCounting: boolean;
  private timeCount: number;
  private timeDuration: number;
  private currentCardSelect: number;
  private isLoseTung: boolean = false;

  public get CurrentCardSelecting(): number {
    return this.currentCardSelect;
  }

  setInfo(data: any, isMe: boolean) {
    if (data) {
      this.Data = data;
      const name = data.Nickname;
      const price = data.Balance;
      let cards: number[] = data.Hand?.HandCards || [];
      let shown: any[] = data.Hand?.ShownCards || [];

      if (this.Name && name && name.length) this.Name.string = name;
      if (this.Amount) this.Amount.string = price.toLocaleString("vi");

      if (this.PlayerNode) {
        this.PlayerNode.getComponent(Sprite).spriteFrame = App.instance.sprFrameAvatars[data.AvatarId];
        this.PlayerNode.active = true;
      }
      this.PlayerNode.getComponent(Sprite).color = cards && cards.length ? new Color(255, 255, 255) : new Color(100, 100, 100);

      if (this.NonePlayer) this.NonePlayer.active = false;
      if (this.winLabel) this.winLabel.node.active = false;

      if (data.Hand) {
        if (this.CardLayoutNode) {
          this.CardLayoutNode.active = true;
          this.CardLayoutNode.scale = new Vec3(0.8, 0.8, 0.8);
        }

        let cardCount = 6;
        cardCount = cards && cards.length ? cards.length : 6;
        if (this.CardCount) this.CardCount.string = `${cardCount}`;

        if (shown && shown.length) {
          for (let i = 0; i < 6; i++) {
            let cardNode = this.CardLayoutNode.children[i];
            if (shown[i]) {
              cardNode.active = true;
              let comp = cardNode.getComponent(CardItemController);
              if (comp) {
                comp.show(shown[i].OrdinalValue, shown[i].Status == 2);
              }
            } else {
              cardNode.active = false;
            }
          }
        }
      } else {
        if (this.CardLayoutNode) this.CardLayoutNode.active = false;
      }
      if (isMe) {
        if (this.MyCard) {
          if (this.CardCount.node.parent) this.CardCount.node.parent.active = false;
          this.MyCard.active = true;
        }
        if (cards && cards.length) {
          for (let i = 0; i < 6; i++) {
            let cardNode = this.MyCard.children[i];
            if (cards[i]) {
              cardNode.active = true;
              cardNode.setPosition(new Vec3(cardNode.position.x, 0, cardNode.position.z));
              let comp = cardNode.getComponent(CardItemController);
              if (comp) {
                comp.show(cards[i], false, (id: number) => {
                  this.setCurrentSelecting(id);
                });
              }
            } else {
              cardNode.active = false;
            }
          }
        }
      } else {
        if (this.MyCard) this.MyCard.active = false;
        if (this.CardCount.node.parent && cards.length) this.CardCount.node.parent.active = true;
        else this.CardCount.node.parent.active = false;
      }
    }
  }

  showCardEndGame(data: any, win: boolean, timeReset: number, isSpecial: boolean = false) {
    let shown: any[] = data.Hand?.ShownCards || [];
    if (shown && shown.length) {
      let foldCount = 0;
      for (let i = 0; i < 6; i++) {
        let cardNode = this.CardLayoutNode.children[i];
        if (shown[i]) {
          cardNode.active = true;
          let comp = cardNode.getComponent(CardItemController);
          if (comp) {
            comp.show(shown[i].OrdinalValue, shown[i].Status == 2);
          }
          if (shown[i].OrdinalValue === -1) {
            foldCount++;
            this.isLoseTung = foldCount >= 4;
          }
        } else {
          cardNode.active = false;
        }
      }
    }
    this.showWin(win, timeReset, isSpecial);
  }

  setCurrentSelecting(id: number) {
    if (this.MyCard) {
      for (let i = 0; i < 6; i++) {
        const cardNode = this.MyCard.children[i];
        let comp = cardNode.getComponent(CardItemController);
        if (comp) {
          cardNode.setPosition(comp.CardID === id ? new Vec3(cardNode.position.x, 15, cardNode.position.z) : new Vec3(cardNode.position.x, 0, cardNode.position.z));
        }
      }
    }
    this.currentCardSelect = id;
  }

  setEmpty() {
    this.Data = null;
    if (this.PlayerNode) this.PlayerNode.active = false;
    if (this.NonePlayer) this.NonePlayer.active = true;
    if (this.winLabel) this.winLabel.node.active = false;
  }

  showWin(win: boolean, Timeout: number, isSpecial: boolean) {
    var bundle = assetManager.getBundle("Catte");
    const languageCode = LanguageManager.instance.locale;
    const pathLose = this.isLoseTung ? `thua-tung-${languageCode}.png/spriteFrame` : `thua-${languageCode}.png/spriteFrame`;
    const pathWin = isSpecial ? `catte-toitrang-${languageCode}.png/spriteFrame` : `thang-${languageCode}.png/spriteFrame`;
    bundle.load(`res/textures/${win ? pathWin : pathLose}`, SpriteFrame, (err, spr: SpriteFrame) => {
      if (err) {
        console.error("Lỗi khi tải ảnh:", err);
        return;
      }
      if (win) {
        let cover = this.node.getChildByName("Win");
        if (cover) cover.active = true;
      }
      if (this.winLabel) this.winLabel.node.active = true;
      if (this.winLabel) this.winLabel.node.scale = isSpecial ? new Vec3(0.5, 0.5, 0.5) : Vec3.ONE;
      if (this.winLabel) this.winLabel.spriteFrame = spr;

      this.scheduleOnce(() => {
        this.reset();
      }, Timeout ?? 4);
    });
  }

  reset() {
    this.currentCardSelect = 0;
    this.isLoseTung = false;
    if (this.CardCount) this.CardCount.string = `6`;
    this.CardCount.node.parent.active = false;
    if (this.CardLayoutNode) {
      this.CardLayoutNode.active = false;
      this.CardLayoutNode.children.forEach((e) => {
        let comp = e.getComponent(Sprite);
        if (comp) {
          comp.spriteFrame = null;
        }
      });
    }
    if (this.MyCard) {
      this.MyCard.active = false;
      this.MyCard.children.forEach((e) => {
        let comp = e.getComponent(Sprite);
        if (comp) {
          comp.spriteFrame = null;
        }
      });
    }
    if (this.winLabel) this.winLabel.node.active = false;
    if (this.Price) {
      this.Price.node.scale = Vec3.ZERO;
    }
    let cover = this.node.getChildByName("Win");
    if (cover) cover.active = false;
  }

  displayEmotion(id: number) {
    const node = new Node();
    const sprite = node.addComponent(Sprite);
    const uo = node.addComponent(UIOpacity);
    sprite.spriteFrame = App.instance.listEmotionSpr[id] || App.instance.listEmotionSpr[0];
    node.position = new Vec3(0, -15, 0);
    node.active = true;

    this.node.addChild(node);

    tween(uo)
      .delay(0.5).to(1.5, { opacity: 0 }, { easing: "smooth" })
      .start();
    tween(node)
      .delay(0.5).to(1.5, { position: new Vec3(0, 60, 0) }, { easing: "sineOut" })

      .call(() => node.destroy())
      .start();
  }
  showChatMsg(content: string) {
    if (content.indexOf("E__") === 0) {
      const id = parseInt(content.replace("E__", ""));
      this.displayEmotion(id);
      this.spriteChat.node.active = true;
      this.scheduleOnce(() => {
        this.spriteChat.node.active = false;
      }, 2);

      return;
    }

    this.chatMsg.string = content;
    this.chatMsg.node.parent.active = true;
    this.scheduleOnce(() => {
      this.chatMsg.node.parent.active = false;
      this.chatMsg.string = "";
    }, 5);
  }

  showPrize(price: number) {
    const str = price > 0 ? `+${price}` : `${price}`;
    if (this.Price) {
      this.Price.string = str;
      this.Price.node.scale;
      tween(this.Price.node).to(0.25, { scale: Vec3.ONE }).start();
    }
  }

  showTimming(time: number) {
    this.timeCount = time;
    this.timeDuration = time;
    this.isCounting = true;
    if (this.Cover) {
      this.Cover.spriteFrame = this.CoverSprites[0];
    }
  }

  stopTimeMiming() {
    this.isCounting = false;
    if (this.Cover) {
      this.Cover.spriteFrame = null;
    }
  }

  protected update(dt: number): void {
    if (this.isCounting) {
      this.timeDuration -= dt;
      this.Cover.fillRange = this.timeDuration / this.timeCount;
      if (this.timeDuration < 6 && this.timeDuration > 3) {
        this.Cover.spriteFrame = this.CoverSprites[1];
      } else if (this.timeDuration < 3) {
        this.Cover.spriteFrame = this.CoverSprites[2];
      }
      if (this.timeDuration < 0) {
        this.isCounting = false;
      }
    }
  }
}

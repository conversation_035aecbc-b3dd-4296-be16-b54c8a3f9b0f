{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "1c43c923-a918-4d7c-99b6-6dd1cd65990a", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "1c43c923-a918-4d7c-99b6-6dd1cd65990a@6c48a", "displayName": "thang-tung-id.png", "id": "6c48a", "name": "texture", "userData": {"minfilter": "linear", "magfilter": "linear", "wrapModeT": "clamp-to-edge", "wrapModeS": "clamp-to-edge", "mipfilter": "none", "imageUuidOrDatabaseUri": "1c43c923-a918-4d7c-99b6-6dd1cd65990a", "isUuid": true, "visible": false, "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "1c43c923-a918-4d7c-99b6-6dd1cd65990a@f9941", "displayName": "thang-tung-id.png", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 783, "height": 116, "rawWidth": 783, "rawHeight": 116, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "imageUuidOrDatabaseUri": "1c43c923-a918-4d7c-99b6-6dd1cd65990a@6c48a", "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-391.5, -58, 0, 391.5, -58, 0, -391.5, 58, 0, 391.5, 58, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 116, 783, 116, 0, 0, 783, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-391.5, -58, 0], "maxPos": [391.5, 58, 0]}, "isUuid": true, "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": false, "hasAlpha": true, "redirect": "1c43c923-a918-4d7c-99b6-6dd1cd65990a@6c48a"}}
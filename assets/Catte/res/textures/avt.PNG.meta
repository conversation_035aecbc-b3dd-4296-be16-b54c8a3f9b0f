{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "0f163f1e-8215-40e8-8a88-ead91ff4d9a9", "files": [".json", ".png"], "subMetas": {"avt": {"ver": "1.0.4", "uuid": "b2e819e5-dbba-44a5-b4dd-ecf546c5a55e", "rawTextureUuid": "99dbafdf-b663-4348-99f8-4f33046f140b", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -347, "offsetY": -253, "trimX": 521, "trimY": 701, "width": 184, "height": 184, "rawWidth": 1920, "rawHeight": 1080, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "importer": "*", "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "0f163f1e-8215-40e8-8a88-ead91ff4d9a9@6c48a", "displayName": "avt", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "minfilter": "linear", "magfilter": "linear", "mipfilter": "nearest", "anisotropy": 0, "isUuid": true, "imageUuidOrDatabaseUri": "0f163f1e-8215-40e8-8a88-ead91ff4d9a9", "visible": false}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "0f163f1e-8215-40e8-8a88-ead91ff4d9a9@f9941", "displayName": "avt", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": -347, "offsetY": -253, "trimX": 521, "trimY": 701, "width": 184, "height": 184, "rawWidth": 1920, "rawHeight": 1080, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-92, -92, 0, 92, -92, 0, -92, 92, 0, 92, 92, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [521, 379, 705, 379, 521, 195, 705, 195], "nuv": [0.2713541666666667, 0.18055555555555555, 0.3671875, 0.18055555555555555, 0.2713541666666667, 0.3509259259259259, 0.3671875, 0.3509259259259259], "minPos": [-92, -92, 0], "maxPos": [92, 92, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "0f163f1e-8215-40e8-8a88-ead91ff4d9a9@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "0f163f1e-8215-40e8-8a88-ead91ff4d9a9@6c48a"}}
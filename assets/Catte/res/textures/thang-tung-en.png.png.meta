{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "e3e64b38-bf08-4d15-8bd2-932bbdc0f5ab", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "e3e64b38-bf08-4d15-8bd2-932bbdc0f5ab@6c48a", "displayName": "thang-tung-en.png", "id": "6c48a", "name": "texture", "userData": {"minfilter": "linear", "magfilter": "linear", "wrapModeT": "clamp-to-edge", "wrapModeS": "clamp-to-edge", "mipfilter": "none", "imageUuidOrDatabaseUri": "e3e64b38-bf08-4d15-8bd2-932bbdc0f5ab", "isUuid": true, "visible": false, "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "e3e64b38-bf08-4d15-8bd2-932bbdc0f5ab@f9941", "displayName": "thang-tung-en.png", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 635, "height": 95, "rawWidth": 635, "rawHeight": 95, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "imageUuidOrDatabaseUri": "e3e64b38-bf08-4d15-8bd2-932bbdc0f5ab@6c48a", "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-317.5, -47.5, 0, 317.5, -47.5, 0, -317.5, 47.5, 0, 317.5, 47.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 95, 635, 95, 0, 0, 635, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-317.5, -47.5, 0], "maxPos": [317.5, 47.5, 0]}, "isUuid": true, "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": false, "hasAlpha": true, "redirect": "e3e64b38-bf08-4d15-8bd2-932bbdc0f5ab@6c48a"}}
[{"__type__": "cc.Prefab", "_name": "Catte", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Catte", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 12}, {"__id__": 28}, {"__id__": 44}, {"__id__": 222}, {"__id__": 491}, {"__id__": 515}], "_active": true, "_components": [{"__id__": 537}, {"__id__": 539}, {"__id__": 541}, {"__id__": 543}, {"__id__": 545}], "_prefab": {"__id__": 547}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}, {"__id__": 9}], "_prefab": {"__id__": 11}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "87323e18-3343-45f2-ad23-bfea1d93a942@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18Q1J2YkxB3JucuigRadb+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1920, "_originalHeight": 1080, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dse95IqRCIo/kF9ZEWHlg"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "adLtFQX2RLCLi5fSeUy0Rz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 10}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ed+w0+zr9Gz76s8PCTh985"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1eZroV+9KoqayxYSSP0jZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BackgroundVip", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 13}], "_active": true, "_components": [{"__id__": 21}, {"__id__": 23}, {"__id__": 25}], "_prefab": {"__id__": 27}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -35, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bobai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 14}, {"__id__": 16}, {"__id__": 18}], "_prefab": {"__id__": 20}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 35, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 15}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 53, "b": 51, "a": 255}, "_string": "CATTE", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 60, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 64, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7c24553e-a5a9-4fca-85eb-bd4b3814664d", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "788Qdj3qRDjrVRNhEaaMFJ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 17}, "_opacity": 76, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "72t974lRtAqoJ36nTCes7W"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 19}, "_contentSize": {"__type__": "cc.Size", "width": 161.513671875, "height": 80.64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acA9yfI61MHJa9+TVIN9r/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6cGR7qO6xN2aWZUd7njbd4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 22}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "29fed6fc-09b9-4515-8d9c-7af2fbbecf86@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3Lie2Nq1K9LwPj/1arqif"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 24}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1Q6Iy9RxJVYWGwe1dLxfO"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 26}, "_contentSize": {"__type__": "cc.Size", "width": 1617, "height": 745}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8eTcYwtANIH4HGl0MWhJI3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e7yDFYDHJCbbo+/kA9q2uY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BackgroundNormal", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 29}], "_active": true, "_components": [{"__id__": 37}, {"__id__": 39}, {"__id__": 41}], "_prefab": {"__id__": 43}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -35, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bobai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 28}, "_children": [], "_active": true, "_components": [{"__id__": 30}, {"__id__": 32}, {"__id__": 34}], "_prefab": {"__id__": 36}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 35, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 31}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 81, "g": 59, "b": 33, "a": 255}, "_string": "CATTE", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 60, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 64, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7c24553e-a5a9-4fca-85eb-bd4b3814664d", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5SB4TigpAd7TboNn2wDze"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 33}, "_opacity": 76, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9aIWi2ZB5IB7EMKcTWZ6GE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 35}, "_contentSize": {"__type__": "cc.Size", "width": 161.513671875, "height": 80.64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71MVMYFRhNXJY8wv7lk1Pf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5d0dPzs4FIr4YC94S0JX1A", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 38}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0e8b970d-eaf6-4eaf-b29e-79358596b914@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36R2k8QXpLMbDfPkcNT2xk"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 40}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eRIr+q49HgLa/PBh+juNe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 42}, "_contentSize": {"__type__": "cc.Size", "width": 1616, "height": 745}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5b5Ux9NGNHCYspwNc5gwX7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "34ZrCCpipMEJA16Np0JZ7w", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Game", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 45}, {"__id__": 55}, {"__id__": 75}, {"__id__": 110}, {"__id__": 145}, {"__id__": 180}], "_active": true, "_components": [{"__id__": 215}, {"__id__": 217}, {"__id__": 219}], "_prefab": {"__id__": 221}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "dealer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 44}, "_children": [], "_active": true, "_components": [{"__id__": 46}, {"__id__": 48}, {"__id__": 50}, {"__id__": 52}], "_prefab": {"__id__": 54}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 130, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": {"__id__": 47}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1f1813af-0785-4a4c-b796-d54321fb1474@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7bf6IN6ZBeICMazdiuKwa"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": {"__id__": 49}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6biqb4WFtHDqFskPbUgEQZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": {"__id__": 51}, "_contentSize": {"__type__": "cc.Size", "width": 260, "height": 390}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91EZ33ndNBMI4YP7nVfxsZ"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": {"__id__": 53}, "playOnLoad": false, "_clips": [{"__uuid__": "22a47194-60c5-484d-aab0-67cc27bd13fb", "__expectedType__": "cc.AnimationClip"}, {"__uuid__": "dd950f4b-0078-497a-bafc-d4989f49cd87", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "22a47194-60c5-484d-aab0-67cc27bd13fb", "__expectedType__": "cc.AnimationClip"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fUnKU4H5GdqVLoauAG8F9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "81WyJC9KdMAqiLvqpcsuA4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 44}, "_prefab": {"__id__": 56}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 55}, "asset": {"__uuid__": "85975dbe-6ce4-45b8-95b6-764406af485e", "__expectedType__": "cc.Prefab"}, "fileId": "1aB+MOgstFj4C90Ld77OnH", "instance": {"__id__": 57}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "bcOZNl7ZRKbrFKBg9k9dNH", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 58}, {"__id__": 60}, {"__id__": 61}, {"__id__": 62}, {"__id__": 63}, {"__id__": 65}, {"__id__": 67}, {"__id__": 69}, {"__id__": 70}, {"__id__": 71}, {"__id__": 73}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 59}, "propertyPath": ["_name"], "value": "SlotItem1"}, {"__type__": "cc.TargetInfo", "localID": ["1aB+MOgstFj4C90Ld77OnH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 59}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -330, "y": -285, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 59}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 59}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 64}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["77p5Qk1/ZAz65u2/+OXMk8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 110, "y": 150, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3ct5D2wWdMqpb/qEb4M8nm"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 68}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 110, "y": -60, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["57MealDrxIvIfO4RM+b4hC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 68}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 72}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["a2X9LXUxFPPaR811BVbq70"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 74}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["d808IevUZK+LSitvQAHaOB"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 44}, "_prefab": {"__id__": 76}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 75}, "asset": {"__uuid__": "85975dbe-6ce4-45b8-95b6-764406af485e", "__expectedType__": "cc.Prefab"}, "fileId": "1aB+MOgstFj4C90Ld77OnH", "instance": {"__id__": 77}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "a7YIgD+0RPDZUwlUL7MTym", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 78}, {"__id__": 80}, {"__id__": 81}, {"__id__": 82}, {"__id__": 83}, {"__id__": 85}, {"__id__": 87}, {"__id__": 89}, {"__id__": 90}, {"__id__": 91}, {"__id__": 93}, {"__id__": 95}, {"__id__": 96}, {"__id__": 98}, {"__id__": 100}, {"__id__": 102}, {"__id__": 104}, {"__id__": 106}, {"__id__": 108}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 79}, "propertyPath": ["_name"], "value": "SlotItem2"}, {"__type__": "cc.TargetInfo", "localID": ["1aB+MOgstFj4C90Ld77OnH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 79}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 400, "y": 330, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 79}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 79}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 84}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["77p5Qk1/ZAz65u2/+OXMk8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 86}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -100, "y": -160, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3ct5D2wWdMqpb/qEb4M8nm"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 88}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 110, "y": -60, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["57MealDrxIvIfO4RM+b4hC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 88}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 86}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 92}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["a2X9LXUxFPPaR811BVbq70"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 94}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["d808IevUZK+LSitvQAHaOB"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 84}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -160, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 97}, "propertyPath": ["_anchorPoint"], "value": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}}, {"__type__": "cc.TargetInfo", "localID": ["e4GiYgvrVP0ZmQXio+pSQ9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 99}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -335, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["7b+TD6IYlHcJCbpOrGL6Me"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 101}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -280, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["4e3drhVPtDXaJn44I/qIqQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 103}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -225, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b91MYXqC9PYpAzrpxonGFS"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 105}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -170, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b8V9eOa6FAjqYjIgMANLu9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 107}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -115, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8aPS94B7FFu57BtTjsTBtt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 109}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -60, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["ebxTbGDcNNDpgzeCYhnmwt"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 44}, "_prefab": {"__id__": 111}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 110}, "asset": {"__uuid__": "85975dbe-6ce4-45b8-95b6-764406af485e", "__expectedType__": "cc.Prefab"}, "fileId": "1aB+MOgstFj4C90Ld77OnH", "instance": {"__id__": 112}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "d4FsyA6JhE9b9MVi+jBdZE", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 113}, {"__id__": 115}, {"__id__": 116}, {"__id__": 117}, {"__id__": 118}, {"__id__": 120}, {"__id__": 122}, {"__id__": 124}, {"__id__": 125}, {"__id__": 126}, {"__id__": 128}, {"__id__": 130}, {"__id__": 131}, {"__id__": 133}, {"__id__": 135}, {"__id__": 137}, {"__id__": 139}, {"__id__": 141}, {"__id__": 143}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_name"], "value": "SlotItem3"}, {"__type__": "cc.TargetInfo", "localID": ["1aB+MOgstFj4C90Ld77OnH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -400, "y": 330, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 119}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["77p5Qk1/ZAz65u2/+OXMk8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 121}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 110, "y": -160, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3ct5D2wWdMqpb/qEb4M8nm"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 123}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 110, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["57MealDrxIvIfO4RM+b4hC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 123}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 121}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 127}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["a2X9LXUxFPPaR811BVbq70"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 129}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["d808IevUZK+LSitvQAHaOB"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 119}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 160, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 132}, "propertyPath": ["_anchorPoint"], "value": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}}, {"__type__": "cc.TargetInfo", "localID": ["e4GiYgvrVP0ZmQXio+pSQ9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 134}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 60, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["7b+TD6IYlHcJCbpOrGL6Me"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 136}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 115, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["4e3drhVPtDXaJn44I/qIqQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 138}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 170, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b91MYXqC9PYpAzrpxonGFS"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 140}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 225, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b8V9eOa6FAjqYjIgMANLu9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 142}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 280, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8aPS94B7FFu57BtTjsTBtt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 144}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 335, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["ebxTbGDcNNDpgzeCYhnmwt"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 44}, "_prefab": {"__id__": 146}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 145}, "asset": {"__uuid__": "85975dbe-6ce4-45b8-95b6-764406af485e", "__expectedType__": "cc.Prefab"}, "fileId": "1aB+MOgstFj4C90Ld77OnH", "instance": {"__id__": 147}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "7bAKd7r3pIkqyF5Hr7hBNI", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 148}, {"__id__": 150}, {"__id__": 151}, {"__id__": 152}, {"__id__": 153}, {"__id__": 155}, {"__id__": 157}, {"__id__": 159}, {"__id__": 160}, {"__id__": 161}, {"__id__": 163}, {"__id__": 165}, {"__id__": 166}, {"__id__": 168}, {"__id__": 170}, {"__id__": 172}, {"__id__": 174}, {"__id__": 176}, {"__id__": 178}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_name"], "value": "SlotItem4"}, {"__type__": "cc.TargetInfo", "localID": ["1aB+MOgstFj4C90Ld77OnH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 750, "y": -96, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 154}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["77p5Qk1/ZAz65u2/+OXMk8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 156}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -100, "y": -160, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3ct5D2wWdMqpb/qEb4M8nm"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 158}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 110, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["57MealDrxIvIfO4RM+b4hC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 158}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 156}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 162}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["a2X9LXUxFPPaR811BVbq70"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 164}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["d808IevUZK+LSitvQAHaOB"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 154}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -160, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 167}, "propertyPath": ["_anchorPoint"], "value": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}}, {"__type__": "cc.TargetInfo", "localID": ["e4GiYgvrVP0ZmQXio+pSQ9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 169}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -335, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["7b+TD6IYlHcJCbpOrGL6Me"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 171}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -280, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["4e3drhVPtDXaJn44I/qIqQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 173}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -225, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b91MYXqC9PYpAzrpxonGFS"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 175}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -170, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b8V9eOa6FAjqYjIgMANLu9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 177}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -115, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8aPS94B7FFu57BtTjsTBtt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 179}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -60, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["ebxTbGDcNNDpgzeCYhnmwt"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 44}, "_prefab": {"__id__": 181}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 180}, "asset": {"__uuid__": "85975dbe-6ce4-45b8-95b6-764406af485e", "__expectedType__": "cc.Prefab"}, "fileId": "1aB+MOgstFj4C90Ld77OnH", "instance": {"__id__": 182}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "328xwqi51OPI4RZ3gJKVky", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 183}, {"__id__": 185}, {"__id__": 186}, {"__id__": 187}, {"__id__": 188}, {"__id__": 190}, {"__id__": 192}, {"__id__": 194}, {"__id__": 195}, {"__id__": 196}, {"__id__": 198}, {"__id__": 200}, {"__id__": 201}, {"__id__": 203}, {"__id__": 205}, {"__id__": 207}, {"__id__": 209}, {"__id__": 211}, {"__id__": 213}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 184}, "propertyPath": ["_name"], "value": "SlotItem5"}, {"__type__": "cc.TargetInfo", "localID": ["1aB+MOgstFj4C90Ld77OnH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 184}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -760, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 184}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 184}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 189}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["77p5Qk1/ZAz65u2/+OXMk8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 191}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 110, "y": -90, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3ct5D2wWdMqpb/qEb4M8nm"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 193}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 110, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["57MealDrxIvIfO4RM+b4hC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 193}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 191}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 197}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["a2X9LXUxFPPaR811BVbq70"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 199}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["d808IevUZK+LSitvQAHaOB"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 189}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 160, "y": 60, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 202}, "propertyPath": ["_anchorPoint"], "value": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}}, {"__type__": "cc.TargetInfo", "localID": ["e4GiYgvrVP0ZmQXio+pSQ9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 204}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 60, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["7b+TD6IYlHcJCbpOrGL6Me"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 206}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 115, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["4e3drhVPtDXaJn44I/qIqQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 208}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 170, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b91MYXqC9PYpAzrpxonGFS"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 210}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 225, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b8V9eOa6FAjqYjIgMANLu9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 212}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 280, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8aPS94B7FFu57BtTjsTBtt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 214}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 335, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["ebxTbGDcNNDpgzeCYhnmwt"]}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": {"__id__": 216}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3cRfka37FMhJGQT/wznImy"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": {"__id__": 218}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0an9AL8bdFSbgidpGIQcEY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": {"__id__": 220}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18sO1VAw1IQKmb8sP4NeM3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "36bF2ytExCC5uwHl94fumS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "HUD", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 223}, {"__id__": 233}, {"__id__": 246}, {"__id__": 262}, {"__id__": 275}, {"__id__": 296}, {"__id__": 348}, {"__id__": 444}, {"__id__": 472}], "_active": true, "_components": [{"__id__": 482}, {"__id__": 484}, {"__id__": 486}, {"__id__": 488}], "_prefab": {"__id__": 490}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "InfoLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 222}, "_children": [], "_active": true, "_components": [{"__id__": 224}, {"__id__": 226}, {"__id__": 228}, {"__id__": 230}], "_prefab": {"__id__": 232}, "_lpos": {"__type__": "cc.Vec3", "x": -810, "y": 520, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 223}, "_enabled": true, "__prefab": {"__id__": 225}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 211, "g": 141, "b": 214, "a": 255}, "_string": "BÀN VIP: 1\nMệnh giá: 1000 tipzo\nPhiên: #15", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 28, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "77a4a87c-c228-4ff2-8375-030dfcdd81bf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "97U4sctTRBxIGPub7m6qXn"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 223}, "_enabled": true, "__prefab": {"__id__": 227}, "_alignFlags": 9, "_target": null, "_left": 150, "_right": 0, "_top": 20, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bfGzfD1JtGCagOVG1juKd6"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 223}, "_enabled": true, "__prefab": {"__id__": 229}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1mEYy2DRKl7LkfHuhBA4P"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 223}, "_enabled": true, "__prefab": {"__id__": 231}, "_contentSize": {"__type__": "cc.Size", "width": 183.390625, "height": 91.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4E0V672xK1ZBI6MM8WrVf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "26iNol4a1Eao51rhUqz6FR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "backBtn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 222}, "_children": [], "_active": true, "_components": [{"__id__": 234}, {"__id__": 236}, {"__id__": 239}, {"__id__": 241}, {"__id__": 243}], "_prefab": {"__id__": 245}, "_lpos": {"__type__": "cc.Vec3", "x": -885.5, "y": 470, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 233}, "_enabled": true, "__prefab": {"__id__": 235}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dd1c9c80-7f26-4fea-935d-4aa8b00e2eda@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffMMLv1mxNdbrLTnPqnfjl"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 233}, "_enabled": true, "__prefab": {"__id__": 237}, "clickEvents": [{"__id__": 238}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "dd1c9c80-7f26-4fea-935d-4aa8b00e2eda@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "67c14213-1e00-41eb-8420-38802d0a5b5e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4/TUD7MNBqbPx58PkOeGB"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "8eaa0litndK2LZGqpdr4g8Z", "handler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 233}, "_enabled": true, "__prefab": {"__id__": 240}, "_alignFlags": 9, "_target": null, "_left": 25, "_right": 0, "_top": 20, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9kzJncjJHpqMbK/90yN5r"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 233}, "_enabled": true, "__prefab": {"__id__": 242}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fff9otksJBfbjHsAPHVvom"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 233}, "_enabled": true, "__prefab": {"__id__": 244}, "_contentSize": {"__type__": "cc.Size", "width": 99, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3ZDDecPNLbJaOMuMllXMb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "feO+gkze1PxrpfC8CnVMi+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_thongbao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 222}, "_children": [{"__id__": 247}], "_active": true, "_components": [{"__id__": 255}, {"__id__": 257}, {"__id__": 259}], "_prefab": {"__id__": 261}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 80, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 246}, "_children": [], "_active": true, "_components": [{"__id__": 248}, {"__id__": 250}, {"__id__": 252}], "_prefab": {"__id__": 254}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 247}, "_enabled": true, "__prefab": {"__id__": 249}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "77a4a87c-c228-4ff2-8375-030dfcdd81bf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbnmBbXtVEVJQtN+ckKXjS"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 247}, "_enabled": true, "__prefab": {"__id__": 251}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73n08HFlFMi7UoNT9h4l63"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 247}, "_enabled": true, "__prefab": {"__id__": 253}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bWQPbijNMfZjxiR6rnCEo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ddzghziztC5qc7GgZZHO2r", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 246}, "_enabled": true, "__prefab": {"__id__": 256}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "47e96ed3-1fb9-413a-8ef3-6644419c877d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08lumLi8hMwKXxC13KpsSD"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 246}, "_enabled": true, "__prefab": {"__id__": 258}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1dS5wc4WxBYa1ka+NkA4Ex"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 246}, "_enabled": true, "__prefab": {"__id__": 260}, "_contentSize": {"__type__": "cc.Size", "width": 791, "height": 74}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dVUO9S89AjYYy0N1Ysfng"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ceQMHHeuBNj7jiDxnC9U6F", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "chatBtn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 222}, "_children": [], "_active": true, "_components": [{"__id__": 263}, {"__id__": 265}, {"__id__": 268}, {"__id__": 270}, {"__id__": 272}], "_prefab": {"__id__": 274}, "_lpos": {"__type__": "cc.Vec3", "x": -885.5, "y": -470, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 262}, "_enabled": true, "__prefab": {"__id__": 264}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dfcfbe4a-d1d3-452c-9ba1-b69639f4373f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0ljxV4xJFtqrmN/m6S4Nl"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 262}, "_enabled": true, "__prefab": {"__id__": 266}, "clickEvents": [{"__id__": 267}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "dfcfbe4a-d1d3-452c-9ba1-b69639f4373f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "e22011c9-685b-423c-be30-68921134d7c0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdH0uDfA9GjJUHmbfQxl1s"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "8eaa0litndK2LZGqpdr4g8Z", "handler": "actChat", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 262}, "_enabled": true, "__prefab": {"__id__": 269}, "_alignFlags": 12, "_target": null, "_left": 25, "_right": 0, "_top": 15, "_bottom": 20, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2bq6zoTNKAYw59DnSTKsa"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 262}, "_enabled": true, "__prefab": {"__id__": 271}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8ksoO58JJar94bHgEHort"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 262}, "_enabled": true, "__prefab": {"__id__": 273}, "_contentSize": {"__type__": "cc.Size", "width": 99, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3/cDCqfxAG4rv+2HJ9TyE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1dcL7VCLxEO5NnkhJUnqeM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "soundBtn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 222}, "_children": [{"__id__": 276}], "_active": false, "_components": [{"__id__": 284}, {"__id__": 286}, {"__id__": 289}, {"__id__": 291}, {"__id__": 293}], "_prefab": {"__id__": 295}, "_lpos": {"__type__": "cc.Vec3", "x": -809, "y": -489, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "soundICon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 275}, "_children": [], "_active": true, "_components": [{"__id__": 277}, {"__id__": 279}, {"__id__": 281}], "_prefab": {"__id__": 283}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 276}, "_enabled": true, "__prefab": {"__id__": 278}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeqORDpRtJBJ8mwcL/RdFW"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 276}, "_enabled": true, "__prefab": {"__id__": 280}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2LwO2XSpODbhM+WXpn1Fy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 276}, "_enabled": true, "__prefab": {"__id__": 282}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "678oJMQYZLRbkClTsE6Lsb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e4F7K+lTNI5ITgd66LFLjo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 285}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60FTkJJehGjIIU0wZuSb0i"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 287}, "clickEvents": [{"__id__": 288}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dhP6cHoxFLLi7bkuRV9D6"}, {"__type__": "cc.ClickEvent", "target": null, "component": "", "_componentId": "", "handler": "", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 290}, "_alignFlags": 12, "_target": null, "_left": 115, "_right": 0, "_top": 15, "_bottom": 15, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85BoLiSFdPhLOxPVpyDeac"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 292}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dc/XAEsahG25UMNbREAf0T"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 294}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33gXlkWL5Bk63lGrRY0eDX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7eIUZa+kNAo6FEbwBAfs+a", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "GroupBtns", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 222}, "_children": [{"__id__": 297}, {"__id__": 318}], "_active": false, "_components": [{"__id__": 339}, {"__id__": 341}, {"__id__": 343}, {"__id__": 345}], "_prefab": {"__id__": 347}, "_lpos": {"__type__": "cc.Vec3", "x": 935, "y": -470, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "buttonUp", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 296}, "_children": [{"__id__": 298}], "_active": true, "_components": [{"__id__": 308}, {"__id__": 310}, {"__id__": 313}, {"__id__": 315}], "_prefab": {"__id__": 317}, "_lpos": {"__type__": "cc.Vec3", "x": -298, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 297}, "_children": [], "_active": true, "_components": [{"__id__": 299}, {"__id__": 301}, {"__id__": 303}, {"__id__": 305}], "_prefab": {"__id__": 307}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 298}, "_enabled": true, "__prefab": {"__id__": 300}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "ÚP", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 1, "_enableWrapText": false, "_font": {"__uuid__": "7c24553e-a5a9-4fca-85eb-bd4b3814664d", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14sW1WjZ1D07FM2p1MiiHf"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 298}, "_enabled": true, "__prefab": {"__id__": 302}, "id": "ca03_1", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96GNx9v69HmKW58R2m2j0L"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 298}, "_enabled": true, "__prefab": {"__id__": 304}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90GuyRyDNLJ67gNRlypy5z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 298}, "_enabled": true, "__prefab": {"__id__": 306}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4qjlspTtFm7HP0CBuW2Qz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "11huTu83ZCt6v9E/AFTSLn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 297}, "_enabled": true, "__prefab": {"__id__": 309}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "23283d76-dba3-4853-be16-7211dd47e573@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84sN4lQNdH5qFAxAmGeZwA"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 297}, "_enabled": true, "__prefab": {"__id__": 311}, "clickEvents": [{"__id__": 312}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "23283d76-dba3-4853-be16-7211dd47e573@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "22905e4d-6a9a-47b6-a3d0-a311226dad08@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "22905e4d-6a9a-47b6-a3d0-a311226dad08@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "22905e4d-6a9a-47b6-a3d0-a311226dad08@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1c9dsMeKhKf7K4EJG1CoVf"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "b617bBOJUVLqbfzyGFprnYZ", "handler": "faceDownCard", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 297}, "_enabled": true, "__prefab": {"__id__": 314}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27ZULvFjRLMZJ8vktWqLc+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 297}, "_enabled": true, "__prefab": {"__id__": 316}, "_contentSize": {"__type__": "cc.Size", "width": 182, "height": 105}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "achznTbZtBj5S+U9S8uGVI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ebd6lLm1FO8p/UVEwagvea", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "buttonDanh", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 296}, "_children": [{"__id__": 319}], "_active": true, "_components": [{"__id__": 329}, {"__id__": 331}, {"__id__": 334}, {"__id__": 336}], "_prefab": {"__id__": 338}, "_lpos": {"__type__": "cc.Vec3", "x": -91, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 318}, "_children": [], "_active": true, "_components": [{"__id__": 320}, {"__id__": 322}, {"__id__": 324}, {"__id__": 326}], "_prefab": {"__id__": 328}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 321}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "ĐÁNH", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 1, "_enableWrapText": false, "_font": {"__uuid__": "7c24553e-a5a9-4fca-85eb-bd4b3814664d", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47MJpSI2pOi6zTjVXmi2be"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 323}, "id": "ca70", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eehP/m1m5BKZN9aEHquKVr"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 325}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81O6JRshFK6bHYZgO2n6CL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 327}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d47WXESYxIZqMVH61p4Cm/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bf0yLjqOxD+pI3wNdJT/zq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 318}, "_enabled": true, "__prefab": {"__id__": 330}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "23283d76-dba3-4853-be16-7211dd47e573@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cN7+pX5BNxZzrpf2sPjBd"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 318}, "_enabled": true, "__prefab": {"__id__": 332}, "clickEvents": [{"__id__": 333}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "23283d76-dba3-4853-be16-7211dd47e573@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "22905e4d-6a9a-47b6-a3d0-a311226dad08@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "22905e4d-6a9a-47b6-a3d0-a311226dad08@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 318}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7dc2l9UXRMEpHUwl7JGtxt"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "b617bBOJUVLqbfzyGFprnYZ", "handler": "playCard", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 318}, "_enabled": true, "__prefab": {"__id__": 335}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25riCFlLpFioNCpfOnP8q7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 318}, "_enabled": true, "__prefab": {"__id__": 337}, "_contentSize": {"__type__": "cc.Size", "width": 182, "height": 105}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38ybiyoVlI7p9OM5Gt/UgY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80Z7YSf+ZGOZ7qIIo1cZZx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 296}, "_enabled": true, "__prefab": {"__id__": 340}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 25, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfqPgXjn1L45MfL9rtalMo"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 296}, "_enabled": true, "__prefab": {"__id__": 342}, "_alignFlags": 36, "_target": null, "_left": 0, "_right": 25, "_top": 0, "_bottom": 20, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26miHVoJZIKLZdEx3t1hWB"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 296}, "_enabled": true, "__prefab": {"__id__": 344}, "_opacity": 150, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f78EH0ax1HtZMX66iowXbI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 296}, "_enabled": true, "__prefab": {"__id__": 346}, "_contentSize": {"__type__": "cc.Size", "width": 389, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "366hKTpc1KWrrypoXOkOkK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "945Jf2UqpBGr+Q/RqgfJGL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "WinPopUp", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 222}, "_children": [{"__id__": 349}, {"__id__": 427}], "_active": true, "_components": [{"__id__": 437}, {"__id__": 439}, {"__id__": 441}], "_prefab": {"__id__": 443}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 35, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "toitrangPopUp", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 348}, "_children": [{"__id__": 350}], "_active": true, "_components": [{"__id__": 418}, {"__id__": 420}, {"__id__": 422}, {"__id__": 424}], "_prefab": {"__id__": 426}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "CardsLayout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 349}, "_children": [{"__id__": 351}, {"__id__": 361}, {"__id__": 371}, {"__id__": 381}, {"__id__": 391}, {"__id__": 401}], "_active": true, "_components": [{"__id__": 411}, {"__id__": 413}, {"__id__": 415}], "_prefab": {"__id__": 417}, "_lpos": {"__type__": "cc.Vec3", "x": -150, "y": 125, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 0.8}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "card1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 350}, "_children": [], "_active": true, "_components": [{"__id__": 352}, {"__id__": 354}, {"__id__": 356}, {"__id__": 358}], "_prefab": {"__id__": 360}, "_lpos": {"__type__": "cc.Vec3", "x": 60, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 351}, "_enabled": true, "__prefab": {"__id__": 353}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05/T/OeXhAjIAOx50PBW8l"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 351}, "_enabled": true, "__prefab": {"__id__": 355}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41qoeJQLdGor4JgH4yuFLc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 351}, "_enabled": true, "__prefab": {"__id__": 357}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65BS8gdY9PqaTAKgLjCgb0"}, {"__type__": "c8d57L55P9JmJOS7QKAmKlk", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 351}, "_enabled": true, "__prefab": {"__id__": 359}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6aIIG+glZNzaE8Kb7ifGno"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "81L0SzbtJDr63i5K0Y+Cra", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "card2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 350}, "_children": [], "_active": true, "_components": [{"__id__": 362}, {"__id__": 364}, {"__id__": 366}, {"__id__": 368}], "_prefab": {"__id__": 370}, "_lpos": {"__type__": "cc.Vec3", "x": 115, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": {"__id__": 363}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a62iP5G0lM8ZVMov83R3p8"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": {"__id__": 365}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0PB7F88lK7oZkQSWeDx2t"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": {"__id__": 367}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "devH3fD+FD+pWlKeOxFrPD"}, {"__type__": "c8d57L55P9JmJOS7QKAmKlk", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": {"__id__": 369}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20wmZRjbtKWZQ1rqbh2E+A"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f3JgkA3mpGUbfks6ysEG+v", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "card3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 350}, "_children": [], "_active": true, "_components": [{"__id__": 372}, {"__id__": 374}, {"__id__": 376}, {"__id__": 378}], "_prefab": {"__id__": 380}, "_lpos": {"__type__": "cc.Vec3", "x": 170, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 371}, "_enabled": true, "__prefab": {"__id__": 373}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84d97QVnREDbsyDeIHCNgN"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 371}, "_enabled": true, "__prefab": {"__id__": 375}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f52T6dB7pOe59wthe2MP9p"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 371}, "_enabled": true, "__prefab": {"__id__": 377}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47jOapZbhKkItMDX9SiOnt"}, {"__type__": "c8d57L55P9JmJOS7QKAmKlk", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 371}, "_enabled": true, "__prefab": {"__id__": 379}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f31R6GsWhHQ7Ui04fhbMn5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7aTZy58MBI1J5rSEHYc1Vt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "card4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 350}, "_children": [], "_active": true, "_components": [{"__id__": 382}, {"__id__": 384}, {"__id__": 386}, {"__id__": 388}], "_prefab": {"__id__": 390}, "_lpos": {"__type__": "cc.Vec3", "x": 225, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 381}, "_enabled": true, "__prefab": {"__id__": 383}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddqkmnKQxFQbNU6FSsAHS1"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 381}, "_enabled": true, "__prefab": {"__id__": 385}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9ekfH3fVBdJd7n2SbArEU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 381}, "_enabled": true, "__prefab": {"__id__": 387}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ef4W57AwZPT7PwAnAX23Sq"}, {"__type__": "c8d57L55P9JmJOS7QKAmKlk", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 381}, "_enabled": true, "__prefab": {"__id__": 389}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8izTUYUpEZ4yyk2Wgs/ue"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "adsJNxjpZDRLthcgbOMJH9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "card5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 350}, "_children": [], "_active": true, "_components": [{"__id__": 392}, {"__id__": 394}, {"__id__": 396}, {"__id__": 398}], "_prefab": {"__id__": 400}, "_lpos": {"__type__": "cc.Vec3", "x": 280, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 391}, "_enabled": true, "__prefab": {"__id__": 393}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "89U0UtqcJCqYqU5EHwdDFd"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 391}, "_enabled": true, "__prefab": {"__id__": 395}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8J966JH9GhZ1WrgXvLS2z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 391}, "_enabled": true, "__prefab": {"__id__": 397}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "431HwhO9hBnIUj77fsWTHY"}, {"__type__": "c8d57L55P9JmJOS7QKAmKlk", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 391}, "_enabled": true, "__prefab": {"__id__": 399}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03ZohU27ZKn61ORwCKmlWI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "17ix7jZ5xBK4IE/5w3HWTt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "card6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 350}, "_children": [], "_active": true, "_components": [{"__id__": 402}, {"__id__": 404}, {"__id__": 406}, {"__id__": 408}], "_prefab": {"__id__": 410}, "_lpos": {"__type__": "cc.Vec3", "x": 335, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 401}, "_enabled": true, "__prefab": {"__id__": 403}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "95rvvLKxlESpNhL9n+t5Qv"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 401}, "_enabled": true, "__prefab": {"__id__": 405}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78VxQCAnNL4ZdVY08KwUSj"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 401}, "_enabled": true, "__prefab": {"__id__": 407}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84cZTcjQJNdb1WhJjlDIH+"}, {"__type__": "c8d57L55P9JmJOS7QKAmKlk", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 401}, "_enabled": true, "__prefab": {"__id__": 409}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8CIoLAFNK1ZLEGn2pgHly"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fai0A0xvxGUovE9DIBr1DM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 350}, "_enabled": true, "__prefab": {"__id__": 412}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": -65, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46DK4YdCNEFZ+Wl0Ird3UD"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 350}, "_enabled": true, "__prefab": {"__id__": 414}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "800O73R+NF8qnCjbzOtyRM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 350}, "_enabled": true, "__prefab": {"__id__": 416}, "_contentSize": {"__type__": "cc.Size", "width": 395, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "72rX7IpP9I8blwNBD3b0FL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b56WZ0BHpI4ZjYyFa770nw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 349}, "_enabled": true, "__prefab": {"__id__": 419}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9aa1e84b-7541-425b-b545-b73761181d8a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36D/2EJ99I26BWTlX9QR95"}, {"__type__": "d12e0gfdzJKtpZd8H4K9/SU", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 349}, "_enabled": true, "__prefab": {"__id__": 421}, "viet": {"__uuid__": "5df6cdcf-76ef-4072-a2db-f89d69f9fd20@f9941", "__expectedType__": "cc.SpriteFrame"}, "eng": {"__uuid__": "9aa1e84b-7541-425b-b545-b73761181d8a@f9941", "__expectedType__": "cc.SpriteFrame"}, "thai": {"__uuid__": "bb36ff7d-b89a-41da-bd19-083a3aac0eab@f9941", "__expectedType__": "cc.SpriteFrame"}, "indo": {"__uuid__": "40500fd0-17cc-4d97-b3ef-02a9be29edfb@f9941", "__expectedType__": "cc.SpriteFrame"}, "cam": {"__uuid__": "12a1a7bb-31d8-4413-b511-aa2a492853c0@f9941", "__expectedType__": "cc.SpriteFrame"}, "china": {"__uuid__": "ed7931d6-038d-4fc2-a9ee-31c3237e1e9b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1f7XRV8Z9EKa2o/ltsMlcv"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 349}, "_enabled": true, "__prefab": {"__id__": 423}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41xvhZ6CxH1ZD6Mqh16h51"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 349}, "_enabled": true, "__prefab": {"__id__": 425}, "_contentSize": {"__type__": "cc.Size", "width": 618, "height": 251}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08wmWxp9NLFp08g5LKZ5et"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "26wmy2n/FJuZjLrHJQySwO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "thangtungPopUp", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 348}, "_children": [], "_active": false, "_components": [{"__id__": 428}, {"__id__": 430}, {"__id__": 432}, {"__id__": 434}], "_prefab": {"__id__": 436}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": {"__id__": 429}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e3e64b38-bf08-4d15-8bd2-932bbdc0f5ab@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bfakRYnWZB8pHimI/yFWF1"}, {"__type__": "d12e0gfdzJKtpZd8H4K9/SU", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": {"__id__": 431}, "viet": null, "eng": null, "thai": null, "indo": null, "cam": null, "china": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fa+yL9ZXxKM4JdWxLS8dj6"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": {"__id__": 433}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1aKEjNwtJ0JTjsshRH7ND"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": {"__id__": 435}, "_contentSize": {"__type__": "cc.Size", "width": 635, "height": 95}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2KA/Jp4tM/YanaaG+BADI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "51cfrgM9hF6Ljg+4/3D2zy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 348}, "_enabled": true, "__prefab": {"__id__": 438}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "da602d17-2e08-4f75-9fb4-342f7c713fde@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b91cIC//VAY4mKDAKVcKr/"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 348}, "_enabled": true, "__prefab": {"__id__": 440}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6dxqr6p5EPoT7HE0mvGkJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 348}, "_enabled": true, "__prefab": {"__id__": 442}, "_contentSize": {"__type__": "cc.Size", "width": 688, "height": 765}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79gWgoPRBBu7DqSkb1jnws"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7fGkellBpPlKh40pzcQxBc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "NotifyBar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 222}, "_children": [{"__id__": 445}], "_active": true, "_components": [{"__id__": 465}, {"__id__": 467}, {"__id__": 469}], "_prefab": {"__id__": 471}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 480, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1.2}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "NotifyBar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 444}, "_children": [{"__id__": 446}], "_active": true, "_components": [{"__id__": 454}, {"__id__": 456}, {"__id__": 458}, {"__id__": 460}, {"__id__": 462}], "_prefab": {"__id__": 464}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 23.17, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "RichText", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 445}, "_children": [], "_active": true, "_components": [{"__id__": 447}, {"__id__": 449}, {"__id__": 451}], "_prefab": {"__id__": 453}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 446}, "_enabled": true, "__prefab": {"__id__": 448}, "_lineHeight": 50, "_string": "<color=#00ff00>Rich</c><color=#0fffff>Text</color>", "_horizontalAlign": 0, "_verticalAlign": 1, "_fontSize": 24, "_fontColor": {"__type__": "cc.Color", "r": 205, "g": 190, "b": 228, "a": 255}, "_maxWidth": 0, "_fontFamily": "<PERSON><PERSON>", "_font": {"__uuid__": "3bc5e0fa-ba28-4f98-a6f5-f7a135c85c51", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_userDefinedFont": {"__uuid__": "3bc5e0fa-ba28-4f98-a6f5-f7a135c85c51", "__expectedType__": "cc.TTFFont"}, "_cacheMode": 0, "_imageAtlas": null, "_handleTouchEvent": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43sn5JjndAQIFxj3dNmZgr"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 446}, "_enabled": true, "__prefab": {"__id__": 450}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62GSoviO1LTaonTjGfM01q"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 446}, "_enabled": true, "__prefab": {"__id__": 452}, "_contentSize": {"__type__": "cc.Size", "width": 80.09765625, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5Z45PtRBPZIrFaLD8syZL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1e6AbQJlZDZqD9PoGdn5vI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 445}, "_enabled": true, "__prefab": {"__id__": 455}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44gIJCtStKub1/r7zCYQve"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 445}, "_enabled": true, "__prefab": {"__id__": 457}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ccEX/LQvFPGr5+RDL7iCPL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 445}, "_enabled": true, "__prefab": {"__id__": 459}, "_contentSize": {"__type__": "cc.Size", "width": 960, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ff7TPohmRHka0cyBQ+Z3Jd"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 445}, "_enabled": true, "__prefab": {"__id__": 461}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 205, "g": 190, "b": 228, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16pzs73wFFyIMTHN+4VAl9"}, {"__type__": "0e88aozF4pCZKWz7aZB1Dcb", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 445}, "_enabled": true, "__prefab": {"__id__": 463}, "listNotify": {"__id__": 445}, "notify": {"__id__": 446}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48n5PHr1ZK3IK7HLqoRNkD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e1RjRnHNFKvqhNpP6u57zY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 444}, "_enabled": true, "__prefab": {"__id__": 466}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8c8fbfcc-e88b-48d4-a48e-3853883a0e34@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98txSYSoBEnatbiZRjkEQe"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 444}, "_enabled": true, "__prefab": {"__id__": 468}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26AfpmOHZAq7d8tdsj7V7p"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 444}, "_enabled": true, "__prefab": {"__id__": 470}, "_contentSize": {"__type__": "cc.Size", "width": 1200, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fevR8jvbRCrrzt5Zo3vDBo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3f2Yriq/FJz40dUwiilAoN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 222}, "_children": [], "_active": false, "_components": [{"__id__": 473}, {"__id__": 475}, {"__id__": 477}, {"__id__": 479}], "_prefab": {"__id__": 481}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -135, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 472}, "_enabled": true, "__prefab": {"__id__": 474}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "<PERSON><PERSON><PERSON>ng đủ người chơi để thực hiện v<PERSON> chơi", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 1, "_enableWrapText": true, "_font": {"__uuid__": "7c24553e-a5a9-4fca-85eb-bd4b3814664d", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "49qmwoPrZH1YZnCNsOkXiR"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 472}, "_enabled": true, "__prefab": {"__id__": 476}, "id": "me10009", "isUpperCase": false, "useCustomFont": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65OAWGZcdAOI023J7aXuVx"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 472}, "_enabled": true, "__prefab": {"__id__": 478}, "_opacity": 62, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3bCBPpWUlJu4N9d+nbFIBp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 472}, "_enabled": true, "__prefab": {"__id__": 480}, "_contentSize": {"__type__": "cc.Size", "width": 800, "height": 40.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68wgpU2oNJyo6+YOQLceET"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "04Gpn4s55EI6NK0liZOFD+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": {"__id__": 483}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "batq4iwZxGZ5Tvm5f3gDSB"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": {"__id__": 485}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b24EeNDuBHZKF0IhDgwi49"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": {"__id__": 487}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4s24tOjVHkpnIumSVoUBO"}, {"__type__": "0873c4ISlxNYIF/p5zupACP", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": {"__id__": 489}, "InfoLabel": {"__id__": 224}, "BackgroundNormal": {"__id__": 28}, "BackgroundVip": {"__id__": 12}, "GroupButtons": {"__id__": 296}, "WinPopUp": {"__id__": 348}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47o78VSjdA3rOZbe5vHyrr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a2/Hf0RdhHwrezO08imwTD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "UI", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 492}, {"__id__": 500}], "_active": true, "_components": [{"__id__": 508}, {"__id__": 510}, {"__id__": 512}], "_prefab": {"__id__": 514}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "PopUp", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 491}, "_children": [], "_active": true, "_components": [{"__id__": 493}, {"__id__": 495}, {"__id__": 497}], "_prefab": {"__id__": 499}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 492}, "_enabled": true, "__prefab": {"__id__": 494}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94X90jcMdN4I8FigjTpvyB"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 492}, "_enabled": true, "__prefab": {"__id__": 496}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1aWwN3Z91AHLrOow5qKhhl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 492}, "_enabled": true, "__prefab": {"__id__": 498}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bTnLt0j9EdZH6jc3WSAj6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "60KlIEnyNPeokCHQSPvAm3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Effect", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 491}, "_children": [], "_active": true, "_components": [{"__id__": 501}, {"__id__": 503}, {"__id__": 505}], "_prefab": {"__id__": 507}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 520, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.ParticleSystem2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 500}, "_enabled": true, "__prefab": {"__id__": 502}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "duration": 5, "emissionRate": 100, "life": 1.5, "lifeVar": 0.5, "angle": 0, "angleVar": 0, "startSize": 40, "startSizeVar": 0, "endSize": -1, "endSizeVar": 0, "startSpin": 0, "startSpinVar": 51, "endSpin": 500, "endSpinVar": 0, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 608, "y": 1}, "emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -906}, "speed": 0, "speedVar": 337, "tangentialAccel": 0, "tangentialAccelVar": 0, "radialAccel": 0, "radialAccelVar": 50, "rotationIsDir": false, "startRadius": 0, "startRadiusVar": 0, "endRadius": 0, "endRadiusVar": 0, "rotatePerS": 0, "rotatePerSVar": 0, "playOnLoad": false, "autoRemoveOnFinish": false, "_preview": true, "preview": true, "_custom": true, "_file": null, "_spriteFrame": {"__uuid__": "931a8295-8875-403e-8b9b-5bb857cb58e5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_totalParticles": 150, "_startColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 191, "a": 255}, "_startColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "_endColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 191, "a": 255}, "_endColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "_positionType": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8781W6aRxNYIyGKNGN1O2V"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 500}, "_enabled": true, "__prefab": {"__id__": 504}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cQujw2sNOjZBSXbJuH36u"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 500}, "_enabled": true, "__prefab": {"__id__": 506}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "49znqlActFWrV8hMA3sB0O"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "11wPnbaZ1GIa+aBbuIAlqk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 491}, "_enabled": true, "__prefab": {"__id__": 509}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c159Rjgi5LfYGqrOl0MhH5"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 491}, "_enabled": true, "__prefab": {"__id__": 511}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7cpAsJzO9KkLS8y3OpYhe1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 491}, "_enabled": true, "__prefab": {"__id__": 513}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39T/eFk6BEwadlduGRFY2Y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2eQ/OnJv5KQYVhkvmmT6Ir", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "SoundManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 516}, {"__id__": 524}], "_active": true, "_components": [{"__id__": 532}, {"__id__": 534}], "_prefab": {"__id__": 536}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "MusicSource", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 515}, "_children": [], "_active": true, "_components": [{"__id__": 517}, {"__id__": 519}, {"__id__": 521}], "_prefab": {"__id__": 523}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 516}, "_enabled": true, "__prefab": {"__id__": 518}, "_clip": null, "_loop": true, "_playOnAwake": true, "_volume": 0.5, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03KudbTYhPuoeaB0wwBb50"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 516}, "_enabled": true, "__prefab": {"__id__": 520}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96VezGD69GGIRlziRWrThi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 516}, "_enabled": true, "__prefab": {"__id__": 522}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddt3oda+ZO8a2hv0ag/t8G"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7bBg3/QxVCwYt+dBrqgm7E", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "EffectSource", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 515}, "_children": [], "_active": true, "_components": [{"__id__": 525}, {"__id__": 527}, {"__id__": 529}], "_prefab": {"__id__": 531}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 524}, "_enabled": true, "__prefab": {"__id__": 526}, "_clip": null, "_loop": false, "_playOnAwake": true, "_volume": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9w+hyGQtIJ4jD1dUVPTHK"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 524}, "_enabled": true, "__prefab": {"__id__": 528}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4dIy9mU7ZNbbIGwHEKjS0E"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 524}, "_enabled": true, "__prefab": {"__id__": 530}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0C9Q6VEBCQqPE6GnMqhSB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "27SaXc7KxNEKisi+7pMpJt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 515}, "_enabled": true, "__prefab": {"__id__": 533}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1d7o9gPd1Jz5x62qdsDVMK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 515}, "_enabled": true, "__prefab": {"__id__": 535}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d85wYRV3RGu5uKntdZlg1A"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d9zH3bXq9NaKL3u8VV56lF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 538}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fEStC0e9IEbV9z/9NPsH7"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 540}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbQYIXd5pPqK7O05noaWXW"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 542}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b72vw33W9OkZbKJ6WaCSAy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 544}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90n8AW34lL6a3UluT5b4uf"}, {"__type__": "8eaa0litndK2LZGqpdr4g8Z", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 546}, "HUD": {"__id__": 488}, "PlayerList": [null, null, null, null, null], "dealer": {"__id__": 46}, "WinPopUp": {"__id__": 348}, "Detail": {"__id__": 246}, "NoityPlayerCount": {"__id__": 472}, "Effect": {"__id__": 501}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4b4zouaIZJvLQw23JBeDVF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "daYKDXVI9LsbQKW5AYVecb", "instance": null, "targetOverrides": [{"__id__": 548}, {"__id__": 550}, {"__id__": 552}, {"__id__": 554}, {"__id__": 556}], "nestedPrefabInstanceRoots": [{"__id__": 180}, {"__id__": 145}, {"__id__": 110}, {"__id__": 75}, {"__id__": 55}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 545}, "sourceInfo": null, "propertyPath": ["PlayerList", "0"], "target": {"__id__": 55}, "targetInfo": {"__id__": 549}}, {"__type__": "cc.TargetInfo", "localID": ["56vahmnydCxJFfN3VPm6/A"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 545}, "sourceInfo": null, "propertyPath": ["PlayerList", "1"], "target": {"__id__": 75}, "targetInfo": {"__id__": 551}}, {"__type__": "cc.TargetInfo", "localID": ["56vahmnydCxJFfN3VPm6/A"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 545}, "sourceInfo": null, "propertyPath": ["PlayerList", "2"], "target": {"__id__": 110}, "targetInfo": {"__id__": 553}}, {"__type__": "cc.TargetInfo", "localID": ["56vahmnydCxJFfN3VPm6/A"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 545}, "sourceInfo": null, "propertyPath": ["PlayerList", "3"], "target": {"__id__": 145}, "targetInfo": {"__id__": 555}}, {"__type__": "cc.TargetInfo", "localID": ["56vahmnydCxJFfN3VPm6/A"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 545}, "sourceInfo": null, "propertyPath": ["PlayerList", "4"], "target": {"__id__": 180}, "targetInfo": {"__id__": 557}}, {"__type__": "cc.TargetInfo", "localID": ["56vahmnydCxJFfN3VPm6/A"]}]
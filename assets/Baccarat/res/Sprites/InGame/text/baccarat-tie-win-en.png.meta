{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "4dc295cb-b750-412c-a081-8f367ae9ff67", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "4dc295cb-b750-412c-a081-8f367ae9ff67@6c48a", "displayName": "baccarat-tie-win-en", "id": "6c48a", "name": "texture", "userData": {"minfilter": "linear", "magfilter": "linear", "wrapModeT": "clamp-to-edge", "wrapModeS": "clamp-to-edge", "mipfilter": "none", "imageUuidOrDatabaseUri": "4dc295cb-b750-412c-a081-8f367ae9ff67", "isUuid": true, "visible": false, "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "4dc295cb-b750-412c-a081-8f367ae9ff67@f9941", "displayName": "baccarat-tie-win-en", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 141, "height": 72, "rawWidth": 141, "rawHeight": 72, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "imageUuidOrDatabaseUri": "4dc295cb-b750-412c-a081-8f367ae9ff67@6c48a", "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-70.5, -36, 0, 70.5, -36, 0, -70.5, 36, 0, 70.5, 36, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 72, 141, 72, 0, 0, 141, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-70.5, -36, 0], "maxPos": [70.5, 36, 0]}, "isUuid": true, "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": false, "hasAlpha": true, "redirect": "4dc295cb-b750-412c-a081-8f367ae9ff67@6c48a"}}
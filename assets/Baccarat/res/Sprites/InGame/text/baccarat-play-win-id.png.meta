{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "88a2cc02-02bb-4b95-8d4c-13a9ecd6587e", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "88a2cc02-02bb-4b95-8d4c-13a9ecd6587e@6c48a", "displayName": "baccarat-play-win-id", "id": "6c48a", "name": "texture", "userData": {"minfilter": "linear", "magfilter": "linear", "wrapModeT": "clamp-to-edge", "wrapModeS": "clamp-to-edge", "mipfilter": "none", "imageUuidOrDatabaseUri": "88a2cc02-02bb-4b95-8d4c-13a9ecd6587e", "isUuid": true, "visible": false, "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "88a2cc02-02bb-4b95-8d4c-13a9ecd6587e@f9941", "displayName": "baccarat-play-win-id", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 423, "height": 126, "rawWidth": 423, "rawHeight": 126, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "imageUuidOrDatabaseUri": "88a2cc02-02bb-4b95-8d4c-13a9ecd6587e@6c48a", "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-211.5, -63, 0, 211.5, -63, 0, -211.5, 63, 0, 211.5, 63, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 126, 423, 126, 0, 0, 423, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-211.5, -63, 0], "maxPos": [211.5, 63, 0]}, "isUuid": true, "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": false, "hasAlpha": true, "redirect": "88a2cc02-02bb-4b95-8d4c-13a9ecd6587e@6c48a"}}
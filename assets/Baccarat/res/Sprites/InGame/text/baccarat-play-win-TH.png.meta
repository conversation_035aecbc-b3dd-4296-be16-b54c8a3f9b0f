{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "386a5164-f48a-480a-aa60-d8e4ff12f9e3", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "386a5164-f48a-480a-aa60-d8e4ff12f9e3@6c48a", "displayName": "baccarat-play-win-TH", "id": "6c48a", "name": "texture", "userData": {"minfilter": "linear", "magfilter": "linear", "wrapModeT": "clamp-to-edge", "wrapModeS": "clamp-to-edge", "mipfilter": "none", "imageUuidOrDatabaseUri": "386a5164-f48a-480a-aa60-d8e4ff12f9e3", "isUuid": true, "visible": false, "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "386a5164-f48a-480a-aa60-d8e4ff12f9e3@f9941", "displayName": "baccarat-play-win-TH", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 201, "height": 128, "rawWidth": 201, "rawHeight": 128, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "imageUuidOrDatabaseUri": "386a5164-f48a-480a-aa60-d8e4ff12f9e3@6c48a", "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-100.5, -64, 0, 100.5, -64, 0, -100.5, 64, 0, 100.5, 64, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 128, 201, 128, 0, 0, 201, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-100.5, -64, 0], "maxPos": [100.5, 64, 0]}, "isUuid": true, "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": false, "hasAlpha": true, "redirect": "386a5164-f48a-480a-aa60-d8e4ff12f9e3@6c48a"}}
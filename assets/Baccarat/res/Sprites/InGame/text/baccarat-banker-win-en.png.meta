{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "4afe4be8-6ad9-40a6-9390-4a49985eb32e", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "4afe4be8-6ad9-40a6-9390-4a49985eb32e@6c48a", "displayName": "baccarat-banker-win-en", "id": "6c48a", "name": "texture", "userData": {"minfilter": "linear", "magfilter": "linear", "wrapModeT": "clamp-to-edge", "wrapModeS": "clamp-to-edge", "mipfilter": "none", "imageUuidOrDatabaseUri": "4afe4be8-6ad9-40a6-9390-4a49985eb32e", "isUuid": true, "visible": false, "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "4afe4be8-6ad9-40a6-9390-4a49985eb32e@f9941", "displayName": "baccarat-banker-win-en", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 203, "height": 128, "rawWidth": 203, "rawHeight": 128, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "imageUuidOrDatabaseUri": "4afe4be8-6ad9-40a6-9390-4a49985eb32e@6c48a", "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-101.5, -64, 0, 101.5, -64, 0, -101.5, 64, 0, 101.5, 64, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 128, 203, 128, 0, 0, 203, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-101.5, -64, 0], "maxPos": [101.5, 64, 0]}, "isUuid": true, "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": false, "hasAlpha": true, "redirect": "4afe4be8-6ad9-40a6-9390-4a49985eb32e@6c48a"}}
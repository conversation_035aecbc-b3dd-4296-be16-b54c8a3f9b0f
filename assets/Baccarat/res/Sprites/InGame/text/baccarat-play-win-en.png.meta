{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "9c8e9d93-ab4f-47c0-bc22-36ca5f37029b", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "9c8e9d93-ab4f-47c0-bc22-36ca5f37029b@6c48a", "displayName": "baccarat-play-win-en", "id": "6c48a", "name": "texture", "userData": {"minfilter": "linear", "magfilter": "linear", "wrapModeT": "clamp-to-edge", "wrapModeS": "clamp-to-edge", "mipfilter": "none", "imageUuidOrDatabaseUri": "9c8e9d93-ab4f-47c0-bc22-36ca5f37029b", "isUuid": true, "visible": false, "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "9c8e9d93-ab4f-47c0-bc22-36ca5f37029b@f9941", "displayName": "baccarat-play-win-en", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 197, "height": 126, "rawWidth": 197, "rawHeight": 126, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "imageUuidOrDatabaseUri": "9c8e9d93-ab4f-47c0-bc22-36ca5f37029b@6c48a", "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-98.5, -63, 0, 98.5, -63, 0, -98.5, 63, 0, 98.5, 63, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 126, 197, 126, 0, 0, 197, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-98.5, -63, 0], "maxPos": [98.5, 63, 0]}, "isUuid": true, "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": false, "hasAlpha": true, "redirect": "9c8e9d93-ab4f-47c0-bc22-36ca5f37029b@6c48a"}}
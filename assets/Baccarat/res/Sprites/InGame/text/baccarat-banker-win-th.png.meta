{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "62355b65-0a8b-45c1-a125-11c8caf608bf", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "62355b65-0a8b-45c1-a125-11c8caf608bf@6c48a", "displayName": "baccarat-banker-win-th", "id": "6c48a", "name": "texture", "userData": {"minfilter": "linear", "magfilter": "linear", "wrapModeT": "clamp-to-edge", "wrapModeS": "clamp-to-edge", "mipfilter": "none", "imageUuidOrDatabaseUri": "62355b65-0a8b-45c1-a125-11c8caf608bf", "isUuid": true, "visible": false, "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "62355b65-0a8b-45c1-a125-11c8caf608bf@f9941", "displayName": "baccarat-banker-win-th", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 227, "height": 129, "rawWidth": 227, "rawHeight": 129, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "imageUuidOrDatabaseUri": "62355b65-0a8b-45c1-a125-11c8caf608bf@6c48a", "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-113.5, -64.5, 0, 113.5, -64.5, 0, -113.5, 64.5, 0, 113.5, 64.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 129, 227, 129, 0, 0, 227, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-113.5, -64.5, 0], "maxPos": [113.5, 64.5, 0]}, "isUuid": true, "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": false, "hasAlpha": true, "redirect": "62355b65-0a8b-45c1-a125-11c8caf608bf@6c48a"}}
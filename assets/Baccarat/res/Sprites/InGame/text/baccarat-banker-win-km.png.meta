{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "2b8bb92e-76d9-4f6a-b87b-4f9114aeddd5", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "2b8bb92e-76d9-4f6a-b87b-4f9114aeddd5@6c48a", "displayName": "baccarat-banker-win-km", "id": "6c48a", "name": "texture", "userData": {"minfilter": "linear", "magfilter": "linear", "wrapModeT": "clamp-to-edge", "wrapModeS": "clamp-to-edge", "mipfilter": "none", "imageUuidOrDatabaseUri": "2b8bb92e-76d9-4f6a-b87b-4f9114aeddd5", "isUuid": true, "visible": false, "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "2b8bb92e-76d9-4f6a-b87b-4f9114aeddd5@f9941", "displayName": "baccarat-banker-win-km", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 167, "height": 155, "rawWidth": 167, "rawHeight": 155, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "imageUuidOrDatabaseUri": "2b8bb92e-76d9-4f6a-b87b-4f9114aeddd5@6c48a", "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-83.5, -77.5, 0, 83.5, -77.5, 0, -83.5, 77.5, 0, 83.5, 77.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 155, 167, 155, 0, 0, 167, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-83.5, -77.5, 0], "maxPos": [83.5, 77.5, 0]}, "isUuid": true, "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": false, "hasAlpha": true, "redirect": "2b8bb92e-76d9-4f6a-b87b-4f9114aeddd5@6c48a"}}
{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "1405f068-9e1f-4858-a5bc-2191e42c45d6", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "1405f068-9e1f-4858-a5bc-2191e42c45d6@6c48a", "displayName": "baccarat-banker-win-zh", "id": "6c48a", "name": "texture", "userData": {"minfilter": "linear", "magfilter": "linear", "wrapModeT": "clamp-to-edge", "wrapModeS": "clamp-to-edge", "mipfilter": "none", "imageUuidOrDatabaseUri": "1405f068-9e1f-4858-a5bc-2191e42c45d6", "isUuid": true, "visible": false, "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "1405f068-9e1f-4858-a5bc-2191e42c45d6@f9941", "displayName": "baccarat-banker-win-zh", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 165, "height": 146, "rawWidth": 165, "rawHeight": 146, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "imageUuidOrDatabaseUri": "1405f068-9e1f-4858-a5bc-2191e42c45d6@6c48a", "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-82.5, -73, 0, 82.5, -73, 0, -82.5, 73, 0, 82.5, 73, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 146, 165, 146, 0, 0, 165, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-82.5, -73, 0], "maxPos": [82.5, 73, 0]}, "isUuid": true, "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": false, "hasAlpha": true, "redirect": "1405f068-9e1f-4858-a5bc-2191e42c45d6@6c48a"}}
{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "39bd103b-4db2-4a8f-ad62-d14f01d36caa", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "39bd103b-4db2-4a8f-ad62-d14f01d36caa@6c48a", "displayName": "baccarat-gate-1-5 copy", "id": "6c48a", "name": "texture", "userData": {"minfilter": "linear", "magfilter": "linear", "wrapModeT": "clamp-to-edge", "wrapModeS": "clamp-to-edge", "mipfilter": "none", "imageUuidOrDatabaseUri": "39bd103b-4db2-4a8f-ad62-d14f01d36caa", "isUuid": true, "visible": false, "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "39bd103b-4db2-4a8f-ad62-d14f01d36caa@f9941", "displayName": "baccarat-gate-1-5 copy", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 263, "height": 222, "rawWidth": 263, "rawHeight": 222, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "imageUuidOrDatabaseUri": "39bd103b-4db2-4a8f-ad62-d14f01d36caa@6c48a", "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-131.5, -111, 0, 131.5, -111, 0, -131.5, 111, 0, 131.5, 111, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 222, 263, 222, 0, 0, 263, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-131.5, -111, 0], "maxPos": [131.5, 111, 0]}, "isUuid": true, "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": false, "hasAlpha": true, "redirect": "39bd103b-4db2-4a8f-ad62-d14f01d36caa@6c48a"}}
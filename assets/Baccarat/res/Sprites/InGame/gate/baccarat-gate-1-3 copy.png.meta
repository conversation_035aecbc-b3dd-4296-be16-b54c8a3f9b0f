{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "f59b4178-204a-48a3-9ed9-4c76f8dc239d", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "f59b4178-204a-48a3-9ed9-4c76f8dc239d@6c48a", "displayName": "baccarat-gate-1-3 copy", "id": "6c48a", "name": "texture", "userData": {"minfilter": "linear", "magfilter": "linear", "wrapModeT": "clamp-to-edge", "wrapModeS": "clamp-to-edge", "mipfilter": "none", "imageUuidOrDatabaseUri": "f59b4178-204a-48a3-9ed9-4c76f8dc239d", "isUuid": true, "visible": false, "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "f59b4178-204a-48a3-9ed9-4c76f8dc239d@f9941", "displayName": "baccarat-gate-1-3 copy", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 195, "height": 171, "rawWidth": 195, "rawHeight": 171, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "imageUuidOrDatabaseUri": "f59b4178-204a-48a3-9ed9-4c76f8dc239d@6c48a", "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-97.5, -85.5, 0, 97.5, -85.5, 0, -97.5, 85.5, 0, 97.5, 85.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 171, 195, 171, 0, 0, 195, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-97.5, -85.5, 0], "maxPos": [97.5, 85.5, 0]}, "isUuid": true, "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": false, "hasAlpha": true, "redirect": "f59b4178-204a-48a3-9ed9-4c76f8dc239d@6c48a"}}
{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "152b7c0a-51f0-48db-8297-4fd27a9946a5", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "152b7c0a-51f0-48db-8297-4fd27a9946a5@6c48a", "displayName": "baccarat-gate-0-3", "id": "6c48a", "name": "texture", "userData": {"minfilter": "linear", "magfilter": "linear", "wrapModeT": "clamp-to-edge", "wrapModeS": "clamp-to-edge", "mipfilter": "none", "imageUuidOrDatabaseUri": "152b7c0a-51f0-48db-8297-4fd27a9946a5", "isUuid": true, "visible": false, "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "152b7c0a-51f0-48db-8297-4fd27a9946a5@f9941", "displayName": "baccarat-gate-0-3", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 195, "height": 171, "rawWidth": 195, "rawHeight": 171, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "imageUuidOrDatabaseUri": "152b7c0a-51f0-48db-8297-4fd27a9946a5@6c48a", "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-97.5, -85.5, 0, 97.5, -85.5, 0, -97.5, 85.5, 0, 97.5, 85.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 171, 195, 171, 0, 0, 195, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-97.5, -85.5, 0], "maxPos": [97.5, 85.5, 0]}, "isUuid": true, "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": false, "hasAlpha": true, "redirect": "152b7c0a-51f0-48db-8297-4fd27a9946a5@6c48a"}}
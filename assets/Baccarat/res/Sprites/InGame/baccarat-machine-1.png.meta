{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "b76091d4-58e5-483f-baaf-e3c0ec768a39", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "b76091d4-58e5-483f-baaf-e3c0ec768a39@6c48a", "displayName": "baccarat-machine-1", "id": "6c48a", "name": "texture", "userData": {"minfilter": "linear", "magfilter": "linear", "wrapModeT": "clamp-to-edge", "wrapModeS": "clamp-to-edge", "mipfilter": "none", "imageUuidOrDatabaseUri": "b76091d4-58e5-483f-baaf-e3c0ec768a39", "isUuid": true, "visible": false, "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "b76091d4-58e5-483f-baaf-e3c0ec768a39@f9941", "displayName": "baccarat-machine-1", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 230, "height": 216, "rawWidth": 230, "rawHeight": 216, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "imageUuidOrDatabaseUri": "b76091d4-58e5-483f-baaf-e3c0ec768a39@6c48a", "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-115, -108, 0, 115, -108, 0, -115, 108, 0, 115, 108, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 216, 230, 216, 0, 0, 230, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-115, -108, 0], "maxPos": [115, 108, 0]}, "isUuid": true, "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": false, "hasAlpha": true, "redirect": "b76091d4-58e5-483f-baaf-e3c0ec768a39@6c48a"}}
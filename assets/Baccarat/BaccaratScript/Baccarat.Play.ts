import { _decorator, Component, Node, Sprite, SpriteFrame, Label, Prefab, Animation, tween, v3, size, Color, instantiate, sys, UITransform, UIOpacity, Tween, Vec3 } from 'cc';
import CasinoPlayer from "db://assets/Lobby/scripts/common/casino/Casino.Player";
import App from "db://assets/Lobby/scripts/common/App";
import BaccaratSignalRClient from "db://assets/Lobby/scripts/common/networks/BaccaratSignalRClient";
import CasinoLobby from "db://assets/Lobby/scripts/common/casino/Casino.Lobby";
import Configs from "db://assets/Lobby/scripts/common/Config";
import {BroadcastReceiver} from "db://assets/Lobby/scripts/common/BroadcastListener";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import ChatInGame from "db://assets/Lobby/scripts/common/ChatInGame";
import BundleControl from "db://assets/Loading/scripts/BundleControl";

const { ccclass, property, menu } = _decorator;

enum PHASE {
    NONE = 0,
    BET = 1,
    CHIA_BAI = 2,
    RESULT = 3,
}

enum RESULT {
    NONE = 0,
    BANKER_WIN = 1,
    PLAYER_WIN = 2,
    TIE = 3,
}

enum RESULT_PAIR {
    NONE = 0,
    BANKER_PAIR = 1,
    PLAYER_PAIR = 2,
    BOTH = 3,
}

enum GATE {
    BAKER = 1,
    PLAYER = 2,
    TIE = 3,
    BANKER_PAIR = 4,
    PLAYER_PAIR = 5
}

@ccclass
@menu("Baccarat/Play")
export default class BaccaratPlay extends Component {
    static instance: BaccaratPlay = null;

    @property(Node)
    roomDetail: Node = null;
    @property([SpriteFrame])
    listTableSpr: SpriteFrame[] = [];
    @property(Sprite)
    tableSpr: Sprite = null;
    @property(Node)
    chipNodes: Node = null;
    @property(Node)
    boxSettingContainer: Node = null;

    // JOIN ROOM
    @property(Label)
    lblToast: Label = null;
    @property([CasinoPlayer])
    players: CasinoPlayer[] = [];
    @property(CasinoPlayer)
    mePlayer: CasinoPlayer = null;
    @property(Label)
    lblDeck: Label = null;

    // BET
    flagLog: number = -1;
    betLogs: any[] = [];
    @property(Node)
    nodeDisableClearBet: Node = null;
    @property(Node)
    nodeDisableRebet: Node = null;
    @property(Node)
    nodeDisableBetX2: Node = null;
    @property(Node)
    nodeDisableFinishBet: Node = null;
    @property(Node)
    baccaratMachine: Node = null;
    @property(Node)
    playerCardSum: Node = null;
    @property(Node)
    bankerCardSum: Node = null;
    @property([Node])
    playerCards: Node[] = [];
    @property([Node])
    bankerCards: Node[] = [];
    @property(Node)
    playerCorner: Node = null;
    @property(Node)
    bankerCorner: Node = null;
    @property(SpriteFrame)
    cardBack: SpriteFrame = null
    @property([SpriteFrame])
    cardFronts: SpriteFrame[] = [];
    @property(Node)
    chipContainer: Node = null;
    @property([Node])
    betPositions: Node[] = [];
    @property([Label])
    lblBetPositions: Label[] = [];
    betValues = [];
    @property(Label)
    countdownSecond: Label = null;
    @property(Sprite)
    progressSprite: Sprite = null;
    @property(SpriteFrame)
    progressSpriteGreen: SpriteFrame = null;
    @property(SpriteFrame)
    progressSpriteYellow: SpriteFrame = null;
    @property(SpriteFrame)
    progressSpriteRed: SpriteFrame = null;
    amounts = [];
    amounts_1 = [
        1,
        5,
        10,
        50,
        100
    ];
    amounts_2 = [
        1,
        2,
        10,
        20,
        100
    ]

    minBet: number = 0;
    amountSelected: number = 0;

    sessionId: number = 0;
    roomId: number;
    roomValue: number;
    currency: number;

    @property(Node)
    boxSoiCau: Node = null;
    @property(Prefab)
    popupRank: Prefab = null;
    @property(Node)
    popupContainer: Node = null;

    // CAU
    @property(Label)
    lblSessionCau: Label = null;
    @property(Node)
    listCauMini: Node = null;
    @property(Node)
    itemCauMini: Node = null;
    @property([Label])
    listLabelCau: Label[] = [];
    @property(Node)
    listCauLeft: Node = null;
    @property(Node)
    itemCauLeft: Node = null;
    @property(Node)
    listCauRight: Node = null;
    @property(Node)
    itemCauRight: Node = null;
    @property([SpriteFrame])
    cauSpr: SpriteFrame[] = [];
    @property([SpriteFrame])
    cauLeftSpr: SpriteFrame[] = [];
    LABEL_CAU: string[] = ["Banker", "Player", "Tie", "Banker Pair", "Player Pair"];

    // SHOW RESULT
    @property(Node)
    dealerNode: Node = null;
    @property(Node)
    playerWinText: Node = null;
    @property(Node)
    bankerWinText: Node = null;

    
    //BOX DEALER
    @property(Label)
    labelDealerNotify: Label = null;

    //BET TABLE
    @property(Node)
    betTableVip: Node = null;
    @property(Node)
    betTableNormal: Node = null;

    @property(Node)
    guideBG: Node = null;
    @property(Node)
    guide: Node = null;

    init(roomValue: number, currency: number) {
        this.roomValue = roomValue;
        this.currency = currency;
    }

    protected start() {
        BaccaratPlay.instance = this;
        App.instance.showLoading(true);
        BaccaratSignalRClient.getInstance().send('EnterRoom', [this.roomValue, this.currency], (data) => {
            App.instance.showLoading(false);
            if (data.c < 0) {
                App.instance.alertDialog.showMsg(BaccaratSignalRClient.getErrMsg(data.c));
                this.node.destroy();
                return;
            }

            this.joinRoom(data.r);
            if (sys.localStorage.getItem("CA_BA_first_time") === null) {
                sys.localStorage.setItem("CA_BA_first_time", "1");
                this.showGuide();
            }
        });

        this.betTableNormal.active = !CasinoLobby.instance.isTableVip;
        this.betTableVip.active = CasinoLobby.instance.isTableVip;
        this.flagLog = -1;
    }

    onLoad() {
        this.chipNodes.children.forEach((button, index) => {
            const chip = button.getChildByName("chip");
            const text = button.getChildByName("text");

            chip.on(Node.EventType.TOUCH_END, () => {
                const isSelected = chip["_isSelected"];

                if (isSelected) {
                    Tween.stopAllByTarget(chip);
                    chip.y = 0;
                    text.getComponent(Label).color = Color.WHITE;
                    chip["_isSelected"] = false;
                    return;
                }

                this.chipNodes.children.forEach((otherButton) => {
                    const otherChip = otherButton.getChildByName("chip");
                    const otherText = otherButton.getChildByName("text");
                    Tween.stopAllByTarget(otherChip);
                    otherChip.y = 0;
                    otherText.getComponent(Label).color = Color.WHITE;
                    otherChip["_isSelected"] = false;
                });

                text.getComponent(Label).color = new Color(252, 255, 0);
                chip["_isSelected"] = true;
                this.amountSelected = this.amounts[index] * this.minBet;

                tween(chip)
                    .repeatForever(
                        tween()
                            .to(0.3, { y: 15 })
                            .to(0.3, { y: 0 })
                    )
                    .start();
            });
        });

        this.betPositions.forEach((betNode, p: number) => {
            betNode.on(Node.EventType.TOUCH_END, () => {
                if (this.amountSelected == 0) {
                    return;
                }

                this.actBet(this.amountSelected, p + 1);
            });
        });

        BaccaratSignalRClient.getInstance().receive('roomData', (data) => {
            if (data.c < 0) {
                this.showToast(BaccaratSignalRClient.getErrMsg(data.c));
                return;
            }

            this.sessionId = data.r.SessionId;
            this.roomDetail.getChildByName('RoomSession').getComponent(Label).string = App.instance.getTextLang('txt_session') + `: #${this.sessionId}`;
            this.lblSessionCau.string = `#${this.sessionId}`;
            this.lblDeck.string = data.r.Deck;
            this.handleSession(data.r.Session);
            data.r.Players.forEach((player: any) => {
                var playerObj = this.getAllPlayersById(player.AccountId);
                if (playerObj) {
                    playerObj.setCoin(player.Balance);
                }
            });
        });

        BaccaratSignalRClient.getInstance().receive('joinRoom', (data) => {
            if (data.c < 0) {
                this.showToast(BaccaratSignalRClient.getErrMsg(data.c));
                return;
            }

            if (data.r.AccountId != `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                this.randomFreePlayer().set(data.r);
            }
        });

        BaccaratSignalRClient.getInstance().receiveArray('recieveMessage', (accountId: string, _nickname: string, content: string) => {
            var playerRM = this.getAllPlayersById(accountId);
            playerRM.showChatMsg(content);
        });

        BaccaratSignalRClient.getInstance().receive('leaveRoom', (data) => {
            if (data.c < 0) {
                this.showToast(BaccaratSignalRClient.getErrMsg(data.c));
                return;
            }

            for (var i = 0; i < data.r.length; i++) {
                var item = data.r[i];
                if (item.id == `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                    if (item.reason < 0) {
                        App.instance.alertDialog.showMsg(App.instance.getTextLang(`ca${item.reason}`));
                    }
                    BaccaratSignalRClient.getInstance().dontReceive();
                    BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
                    this.node.destroy();
                } else {
                    this.getPlayerById(item.id).leave();
                }
            }
        })

        BaccaratSignalRClient.getInstance().receive('registerLeavingRoom', (data) => {
            var playerRLR = this.getAllPlayersById(data.r.id);
            if (data.r.status) {
                playerRLR.showRegisterQuit();
            } else {
                playerRLR.hideRegisterQuit();
            }
        });

        BaccaratSignalRClient.getInstance().receive('rejectBetting', (data) => {
            let rejectedPlayers = data.map((id: string) => this.getPlayerById(id).lblNickname.string).join(", ");
            this.showToast(`Reject betting from ${rejectedPlayers}`);
        });

        BaccaratSignalRClient.getInstance().receive('clearBetting', (data) => {
            var playerCBId = data.r.id;
            var playerCB = this.getPlayerById(playerCBId);
            this.chipContainer.children.filter(child => child.name.includes(playerCBId)).forEach(chip => {
                this.moveChipToPlayer(chip, this.mePlayer);
            });

            this.cancelBet(playerCBId);
            playerCB.setCoin(data.r.balance);
        });

        BaccaratSignalRClient.getInstance().receive('confirmBetting', (data) => {
            [this.mePlayer, ...this.players].find(player => player.id === data.r.id).showReady();
        });

        BaccaratSignalRClient.getInstance().receive('playerOtherDevice', (data) => {
            this.showToast(BaccaratSignalRClient.getErrMsg(data));
        });

        BaccaratSignalRClient.getInstance().receive('connectionChanged', (data) => {
            var playerCC = this.getPlayerById(data.r.id);
            this.showToast(`${playerCC.lblNickname.string} ${data.status ? "connected" : "disconnected"}`);

            if (data.status) {
                playerCC.leave();
            }
        });

        BaccaratSignalRClient.getInstance().receive('playerBet', (data) => {
            if (data.c < 0) {
                this.showToast(BaccaratSignalRClient.getErrMsg(data.c));
                return;
            }

            var player = this.getPlayerById(data.r.id);
            player.setCoin(data.r.balance);
            this.placeBet(player, data.r.amount, data.r.gate);
            this.saveBetValues(data.r.id, data.r.amount, data.r.gate);
        })

        BaccaratSignalRClient.getInstance().receive('updateHand', (data) => {
            this.moveCardFromMachine(data.IsPlayerHand ? this.playerCards[2] : this.bankerCards[2], data.Cards[2], true);
        });
    }

    saveBetValues(id: string, amount: number, gate: number) {
        if (!Array.isArray(this.betValues[gate - 1])) {
            this.betValues[gate - 1] = [];
        }

        this.betValues[gate - 1].push({ id, amount });
        let totalBet = this.betValues[gate - 1].reduce((sum: any, bet: any) => sum + bet.amount, 0);
        this.lblBetPositions[gate - 1].string = Utils.formatMoney(totalBet, true);
        this.lblBetPositions[gate - 1].node.active = totalBet > 0;
    }

    cancelBet(accountId: string) {
        this.betValues.forEach((bets, index) => {
            if (Array.isArray(bets)) {
                this.betValues[index] = bets.filter(bet => bet.id !== accountId);

                let totalBet = this.betValues[index].reduce((sum: any, bet: any) => sum + bet.amount, 0);
                this.lblBetPositions[index].string = Utils.formatMoney(totalBet, true);
                this.lblBetPositions[index].node.active = totalBet > 0;
            }
        });
    }

    joinRoom(data: any) {
        this.roomId = data.Id;
        this.sessionId = data.SessionId;
        this.lblSessionCau.string = `#${this.sessionId}`;
        this.minBet = data.Value;
        this.roomDetail.getChildByName('RoomTable').getComponent(Label).string = (data.Currency == 0 ? App.instance.getTextLang('tb113') : App.instance.getTextLang('tb112')) + `: ${data.Id}`;
        this.roomDetail.getChildByName('RoomValue').getComponent(Label).string = App.instance.getTextLang('iap38') + ': ' + Utils.formatMoney(data.Value) + ' ' + (data.Currency == 0 ? App.instance.getTextLang('txt_coin') : 'Tipzo');
        this.roomDetail.getChildByName('RoomSession').getComponent(Label).string = App.instance.getTextLang('txt_session') + `: #${data.SessionId}`;
        this.tableSpr.spriteFrame = this.listTableSpr[data.Currency];
        this.lblDeck.string = data.Deck;

        if ([500, 5000, 50000].includes(this.minBet)) {
            this.amounts = this.amounts_2;
        } else {
            this.amounts = this.amounts_1;
        }

        this.chipNodes.children.forEach((button, index) => {
            const text = button.getChildByName("text");
            text.getComponent(Label).string = Utils.formatMoney(this.amounts[index] * this.minBet, true);
            if (index == 0) {
                button.getChildByName("chip").emit(Node.EventType.TOUCH_END);
            }
        });

        var players = data.Players;
        if (players && players.length > 0) {
            for (var i = 0; i < players.length; i++) {
                if (players[i].AccountId != `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                    this.randomFreePlayer().set(players[i]);
                } else {
                    this.mePlayer.set(players[i])
                }
            }
        }

        this.updateStatistic();

        if (data.Session) {
            this.handleSession(data.Session);
        }
    }

    handleSession(session: any) {
        this.unscheduleAllCallbacks();
        var dealerNormal = this.dealerNode.getChildByName('NORMAL');
        // var dealerLacBat = this.dealerNode.getChildByName('LAC_BAT');
        var dealerBET = this.dealerNode.getChildByName('BET');
        // var dealerResult = this.dealerNode.getChildByName('RESULT');
        dealerNormal.active = false;
        // dealerLacBat.active = false;
        dealerBET.active = false;
        // dealerResult.active = false;
        this.labelDealerNotify.string = 'Baccarat';

        if (session.Phrase == PHASE.NONE) {
            dealerNormal.active = true;
            dealerNormal.getComponent(Animation).play();
            return;
        }

        this.countdownSecond.node.parent.active = false;

        if (session.Phrase == PHASE.BET) {
            this.updateTimeout(session.Timeout);
            this.flagLog++;
            this.betLogs = this.betLogs.filter(log => log.flag == this.flagLog - 1);

            dealerBET.active = true;
            dealerBET.getComponent(Animation).play();
            const currency = this.currency == 0 ? App.instance.getTextLang('txt_coin') : 'Tipzo';
            const messages = [
                App.instance.getTextLang('ca157'),
                App.instance.getTextLang('ca148') + ` ${Utils.formatNumber(this.minBet * 10)} ${currency} ` + App.instance.getTextLang('ca156'),
                App.instance.getTextLang('ca150') + ` ${Utils.formatNumber(this.minBet)} ${currency} ` + App.instance.getTextLang('ca151'),
                App.instance.getTextLang('ca148') + ` ${Utils.formatNumber(this.minBet * 100)} ${currency} ` + App.instance.getTextLang('ca155'),
            ];

            let indexMsg = 0;

            this.schedule(() => {
                const parentNode = this.labelDealerNotify.node.parent;
                const opacityComp = parentNode.getComponent(UIOpacity) || parentNode.addComponent(UIOpacity);

                tween(opacityComp)
                    .to(0.5, { opacity: 0 })
                    .call(() => {
                        this.labelDealerNotify.string = messages[indexMsg];
                        indexMsg++;
                        if (indexMsg >= messages.length) indexMsg = 0;
                    })
                    .to(0.2, { opacity: 255 })
                    .start();
            }, 2.5);
            this.nodeDisableClearBet.active = true;
            this.nodeDisableRebet.active = this.betLogs.length == 0;
            this.nodeDisableBetX2.active = this.betLogs.length == 0;
            this.nodeDisableFinishBet.active = true;

            this.playerCards[0].active = false;
            this.bankerCards[0].active = false;
            this.playerCards[1].active = false;
            this.bankerCards[1].active = false;
            this.playerCards[2].active = false;
            this.bankerCards[2].active = false;
            this.playerCorner.active = false;
            this.bankerCorner.active = false;
            this.playerCardSum.active = false;
            this.bankerCardSum.active = false;
            this.playerWinText.active = false;
            this.bankerWinText.active = false;
            Tween.stopAllByTarget(this.bankerWinText);
            Tween.stopAllByTarget(this.playerWinText);
            this.playerCardSum.getComponentInChildren(Label).string = '';
            this.bankerCardSum.getComponentInChildren(Label).string = '';
            this.chipContainer.removeAllChildren();
            for (var i = 0; i < this.betPositions.length; i++) {
                var win = this.betPositions[i].getChildByName("WIN");
                win.active = false;
                var p = win.getChildByName('p');
                if (p) {
                    Tween.stopAllByTarget(p);
                }
            }
            for (var iLBB = 0; iLBB < this.lblBetPositions.length; iLBB++) {
                this.lblBetPositions[iLBB].string = "0";
                this.lblBetPositions[iLBB].node.active = false;
            }
            this.betValues = [];
            [this.mePlayer, ...this.players].forEach(player => {
                player.boxWin.active = false;
                player.isWin = false;
                player.hideReady();
                player.hideRegisterQuit();
                player.hideWinAnimation();
            });
        }

        if (session.Phrase == PHASE.CHIA_BAI) {
            dealerNormal.active = true;
            dealerNormal.getComponent(Animation).play();
            [this.mePlayer, ...this.players].forEach(player => {
                player.hideReady();
            });
            this.nodeDisableClearBet.active = true;
            this.nodeDisableRebet.active = true;
            this.nodeDisableBetX2.active = true;
            this.nodeDisableFinishBet.active = true;
            // dealerLacBat.active = true;
            // dealerLacBat.getComponent(Animation).play();
            this.labelDealerNotify.string = App.instance.getTextLang('ca110');

            this.moveCardFromMachine(this.playerCards[0]);
            this.scheduleOnce(() => {
                this.moveCardFromMachine(this.bankerCards[0]);
            }, 0.5);
            this.scheduleOnce(() => {
                this.moveCardFromMachine(this.playerCards[1], session.PlayerHand.Cards[1]);
            }, 1);
            this.scheduleOnce(() => {
                this.moveCardFromMachine(this.bankerCards[1], session.BankerHand.Cards[1], false, true);
            }, 1.5);

            this.scheduleOnce(() => {
                this.flipCard(this.playerCards[0], session.PlayerHand.Cards[0]);
                this.playerCardSum.active = true;
                this.bankerCardSum.active = true;
            }, 2);
            this.scheduleOnce(() => {
                this.flipCard(this.bankerCards[0], session.BankerHand.Cards[0], true);
            }, 2.5);

            this.scheduleOnce(() => {
                this.flipCorner(this.playerCorner, session.PlayerHand.Sum);
            }, 3);

            this.scheduleOnce(() => {
                this.flipCorner(this.bankerCorner, session.BankerHand.Sum, true);
            }, 4.5);
        }

        if (session.Phrase == PHASE.RESULT) {
            this.labelDealerNotify.string = App.instance.getTextLang('me18');
            dealerNormal.active = true;
            dealerNormal.getComponent(Animation).play();

            this.updateStatistic();
            var result = session.Result;
            this.playerCardSum.getComponentInChildren(Label).string = result.PlayerSum + '';
            this.bankerCardSum.getComponentInChildren(Label).string = result.BankerSum + '';

            switch (result.Winner) {
                case RESULT.NONE:
                    break;
                case RESULT.BANKER_WIN:
                    this.showResultGate(GATE.BAKER);
                    this.bankerWinText.active = true;
                    var bankerWinTextOpacity = this.bankerWinText.getComponent(UIOpacity);
                    tween(this.bankerWinText)
                        .repeatForever(
                            tween().sequence(
                                tween()
                                    .parallel(
                                        tween(bankerWinTextOpacity)
                                            .to(0.4, { opacity: 255 }, { easing: 'sineInOut' }),
                                        tween()
                                            .to(0.4, { scale: v3(1.2, 1.2, 1.2) }, { easing: 'backOut' })
                                    ),
                                tween()
                                    .parallel(
                                        tween(bankerWinTextOpacity)
                                            .to(0.4, { opacity: 180 }, { easing: 'sineInOut' }),
                                        tween()
                                            .to(0.4, { scale: Vec3.ONE }, { easing: 'backIn' })
                                    )
                            )
                        )
                        .start();
                    break;
                case RESULT.PLAYER_WIN:
                    this.showResultGate(GATE.PLAYER);
                    this.playerWinText.active = true;
                    var playerWinTextOpacity = this.playerWinText.getComponent(UIOpacity);
                    tween(this.playerWinText)
                        .repeatForever(
                            tween().sequence(
                                tween()
                                    .parallel(
                                        tween(playerWinTextOpacity)
                                            .to(0.4, { opacity: 255 }, { easing: 'sineInOut' }),
                                        tween()
                                            .to(0.4, { scale: v3(1.2, 1.2, 1.2) }, { easing: 'backOut' })
                                    ),
                                tween()
                                    .parallel(
                                        tween(playerWinTextOpacity)
                                            .to(0.4, { opacity: 180 }, { easing: 'sineInOut' }),
                                        tween()
                                            .to(0.4, { scale: Vec3.ONE }, { easing: 'backIn' })
                                    )
                            )
                        )
                        .start();
                    break;
                case RESULT.TIE:
                    this.showResultGate(GATE.TIE);
                    break;
                default:
                    break;
            }

            switch (result.Pair) {
                case RESULT_PAIR.NONE:
                    break;
                case RESULT_PAIR.BANKER_PAIR:
                    this.showResultGate(GATE.BANKER_PAIR);
                    break;
                case RESULT_PAIR.PLAYER_PAIR:
                    this.showResultGate(GATE.PLAYER_PAIR);
                    break;
                case RESULT_PAIR.BOTH:
                    this.showResultGate(GATE.BANKER_PAIR);
                    this.showResultGate(GATE.PLAYER_PAIR);
                    break;
                default:
                    break;
            }

            this.showResultPrize(session.Prizes);
        }
    }

    showResultGate(gate: GATE) {
        var win = this.betPositions[gate - 1].getChildByName("WIN");
        win.active = true;
        var p = win.getChildByName('p');
        if (p) {
            tween(p)
                .by(1, { angle: 360 })
                .repeatForever()
                .start();
        }
    }

    isWinGate(gate: number) {
        return this.betPositions[gate - 1].getChildByName("WIN").active;
    }

    updateTimeout(timeout: number) {
        const totalTime = timeout;
        let elapsed = 0;
        this.countdownSecond.node.parent.active = true;
        this.schedule(() => {
            elapsed += 0.05;
            const percent = Math.min(elapsed / totalTime, 1);
            this.progressSprite.fillRange = percent;
            this.progressSprite.spriteFrame = percent < 0.5 ? this.progressSpriteGreen : (percent < 0.8 ? this.progressSpriteYellow : this.progressSpriteRed);
            this.progressSprite.node.setScale(-1, 1, 1);
        }, 0.05);

        this.countdownSecond.string = timeout < 10 ? `0${timeout}` : timeout + '';
        this.schedule(() => {
            if (timeout < 0) {
                this.unscheduleAllCallbacks();
                return;
            }
            this.countdownSecond.string = timeout < 10 ? `0${timeout}` : timeout + '';
            timeout--;
        }, 1);
    }

    showResultPrize(prizes: any) {
        const uniquePlayers = new Map<string, CasinoPlayer>();

        prizes.forEach((item: any) => {
            const player = this.getAllPlayersById(item.AccountId);
            if (!uniquePlayers.has(player.id)) {
                uniquePlayers.set(player.id, player);
            }
        });

        uniquePlayers.forEach(player => {
            player.isWin = true;
            player.boxWin.active = true;
            player.boxWin.getChildByName('win').active = true;
            player.boxWin.getChildByName('lose').active = false;
            player.showWinAnimation();
            this.chipContainer.children.filter(child => child.name.includes(player.id)).forEach(chip => {
                var gate = parseInt(chip.name.replace(player.id + "__", ""));
                if (this.isWinGate(gate)) {
                    this.moveChipToPlayer(chip, player, true);
                } else {
                    this.moveChipToDealer(chip);
                }
            });
        });

        const playerPrizeIds = prizes.map((item: any) => item.AccountId);
        [this.mePlayer, ...this.players]
            .filter(player => player.id !== "" && !playerPrizeIds.includes(player.id))
            .forEach(player => {
            var isPlayerBet = false;
            this.chipContainer.children.filter(child => child.name.includes(player.id)).forEach(chip => {
                isPlayerBet = true;
                this.moveChipToDealer(chip);
            });

            this.scheduleOnce(() => {
                if (isPlayerBet) {
                    player.boxWin.active = true;
                    player.boxWin.getChildByName('win').active = false;
                    player.boxWin.getChildByName('lose').active = true;
                }
            }, 0.5);
        });
    }

    randomFreePlayer() {
        for (var i = 0; i < this.players.length; i++) {
            if (this.players[i].id == "") {
                return this.players[i];
            }
        }

        return null;
    }

    getPlayerById(id: string) {
        for (var i = 0; i < this.players.length; i++) {
            if (this.players[i].id == id) {
                return this.players[i];
            }
        }

        return null;
    }

    getChipByAmount(amount: number) {
        return this.chipNodes.children.find((_chip, index) => {
            return this.amounts[index] === amount / this.minBet;
        }).getChildByName("chip");
    }

    getAllPlayersById(id: string) {
        return [this.mePlayer, ...this.players].find(player => player.id === id);
    }

    toggleMenu() {
        this.boxSettingContainer.active = !this.boxSettingContainer.active;
    }

    clearBetting() {
        if (this.nodeDisableClearBet.active) {
            return;
        }

        BaccaratSignalRClient.getInstance().send('ClearBetting', [], (data) => {
            if (data.c < 0) {
                this.showToast(BaccaratSignalRClient.getErrMsg(data.c));
                return;
            }

            this.chipContainer.children.filter(child => child.name.includes(this.mePlayer.id)).forEach(chip => {
                this.moveChipToPlayer(chip, this.mePlayer);
            });
            this.nodeDisableClearBet.active = true;
            this.nodeDisableRebet.active = true;
            this.nodeDisableBetX2.active = true;
            this.nodeDisableFinishBet.active = true;
            this.betLogs = [];
            this.mePlayer.setCoin(data.r.balance);
            this.cancelBet(data.r.id);
        });
    }

    x2Betting() {
        if (this.nodeDisableBetX2.active) {
            return;
        }

        this.actBetFromBetLogs();
        setTimeout(() => {
            this.actBetFromBetLogs();
        }, 500);
    }

    reLastBet() {
        if (this.nodeDisableRebet.active) {
            return;
        }

        this.actBetFromBetLogs();
    }

    actBetFromBetLogs() {
        this.betLogs.forEach(log => {
            if (log.flag == this.flagLog - 1) {
                this.actBet(log.amount, log.gate);
            }
        });
    }

    actBet(amount: number, gate: number) {
        BaccaratSignalRClient.getInstance().send('Bet', [amount, gate], (data) => {
            if (data.c < 0) {
                this.showToast(BaccaratSignalRClient.getErrMsg(data.c));
                return;
            }

            this.nodeDisableClearBet.active = false;
            this.nodeDisableRebet.active = true;
            this.nodeDisableBetX2.active = true;
            this.nodeDisableFinishBet.active = false;

            this.placeBet(this.mePlayer, data.r.amount, data.r.gate);
            this.mePlayer.setCoin(data.r.balance);
            this.saveBetValues(data.r.id, data.r.amount, data.r.gate);

            this.betLogs.push({amount: data.r.amount, gate: data.r.gate, flag: this.flagLog});
        });
    }

    finishBetting() {
        if (this.nodeDisableFinishBet.active) {
            return;
        }

        BaccaratSignalRClient.getInstance().send('FinishBetting', [], (data) => {
            if (data.c < 0) {
                this.showToast(BaccaratSignalRClient.getErrMsg(data.c));
                return;
            }

            this.mePlayer.showReady();
        });
    }

    updateStatistic() {
        this.listCauMini.removeAllChildren();
        BaccaratSignalRClient.getInstance().send('GetGameHistory', [this.roomId], (data) => {
            if (data.c < 0) {
                this.showToast(BaccaratSignalRClient.getErrMsg(data.c));
                return;
            }

            this.drawCau(data.r);
        });
    }

    closePlay() {
        BaccaratSignalRClient.getInstance().send('ExitRoom', [], (data) => {
            if (data.c == 0) {
                BaccaratSignalRClient.getInstance().dontReceive();
                BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
                this.node.destroy();
                return;
            }

            if (data.r.status) {
                this.mePlayer.showRegisterQuit();
                this.showToast(App.instance.getTextLang('me8'));
            } else {
                this.mePlayer.hideRegisterQuit();
                this.showToast(App.instance.getTextLang('me9'));
            }
        })
    }

    placeBet(player: CasinoPlayer, amount: number, position: number) {
        let chip = instantiate(this.getChipByAmount(amount));
        chip.setScale(0.5, 0.5, 0.5);
        this.chipContainer.addChild(chip);

        let startPos2D = player.avatarNode.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
        let endPos2D = this.betPositions[position - 1].getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);

        let startPos = this.node.getComponent(UITransform).convertToNodeSpaceAR(new Vec3(startPos2D.x, startPos2D.y, 0));
        let endPos = this.node.getComponent(UITransform).convertToNodeSpaceAR(new Vec3(endPos2D.x, endPos2D.y, 0));

        const tolerance = 10;
        const existingChips = this.chipContainer.children.filter(child =>
            Vec3.distance(child.getPosition(), endPos) <= tolerance
        );

        if (existingChips.length > 0) {
            let offsetX = 10 * (existingChips.length % 5);
            let offsetY = -10 * Math.floor(existingChips.length / 5);
            endPos.x += offsetX;
            endPos.y += offsetY;
        }

        chip.position = startPos;
        chip.name = `${player.id}__${position}`;

        tween(chip)
            .to(0.5, { position: endPos }, { easing: "sineOut" })
            .start();
    }

    showToast(msg: string) {
        this.lblToast.string = msg;
        this.lblToast.node.active = true;

        this.scheduleOnce(() => {
            this.lblToast.node.active = false;
        }, 2);
    }

    showBoxSoiCau() {
        this.boxSoiCau.active = true;
    }

    hiddenBoxSoiCau(){
        this.boxSoiCau.active = false;
    }

    chatInGame: ChatInGame = null;

    showBoxChat() {
        App.instance.inactivityTimer = 0;
        if (this.chatInGame == null) {
            BundleControl.loadPrefabPopup("prefabs/ChatInGame", (prefab: any) => {
                this.chatInGame = instantiate(prefab).getComponent("ChatInGame");
                this.node.addChild(this.chatInGame.node);
                this.chatInGame.show(Configs.InGameIds.Baccarat);
            });
        } else {
            this.chatInGame.show(Configs.InGameIds.Baccarat);
        }
    }

    actShowPopupRank() {
        let popupRank = instantiate(this.popupRank);
        this.popupContainer.addChild(popupRank);
        // @ts-ignore
        popupRank.getComponent("Casino.PopupRank").showDetail(this.currency, Configs.InGameIds.Baccarat);
    }

    drawCau(data: any) {
        this.listCauLeft.removeAllChildren();
        this.listCauRight.removeAllChildren();
        this.listCauMini.removeAllChildren();

        let listCau = [0, 0, 0, 0, 0];

        data.forEach((resultDTO: any) => {
            let { Winner: winner, Pair: pair, BankerSum, PlayerSum } = resultDTO;
            let sumValue = winner === RESULT.BANKER_WIN ? BankerSum : PlayerSum;

            listCau[winner - 1]++;

            const createItem = (template: Node, spriteFrame: SpriteFrame, labelValue?: string) => {
                let item = instantiate(template);
                item.getComponent(Sprite).spriteFrame = spriteFrame;
                if (winner === RESULT.TIE) {
                    item.getComponentInChildren(Label).node.active = false;
                }

                if (labelValue) {
                    var label = item.getComponentInChildren(Label);
                    label.string = labelValue;
                    switch (winner) {
                        case RESULT.BANKER_WIN:
                            label.color = Color.BLACK.fromHEX("#f72373");
                            break;
                        case RESULT.PLAYER_WIN:
                            label.color = Color.BLACK.fromHEX("#1300cb");
                            break;
                        default:
                            break;
                    }
                }
                return item;
            };

            this.listCauMini.addChild(createItem(this.itemCauMini, this.cauSpr[winner - 1]));
            this.listCauRight.addChild(createItem(this.itemCauRight, this.cauSpr[winner - 1]));
            this.listCauLeft.addChild(createItem(this.itemCauLeft, this.cauLeftSpr[winner - 1], sumValue));

            if (pair === RESULT_PAIR.BANKER_PAIR || pair === RESULT_PAIR.BOTH) {
                listCau[3]++;
                this.listCauLeft.addChild(createItem(this.itemCauLeft, this.cauLeftSpr[3], sumValue));
            }
            if (pair === RESULT_PAIR.PLAYER_PAIR || pair === RESULT_PAIR.BOTH) {
                listCau[4]++;
                this.listCauLeft.addChild(createItem(this.itemCauLeft, this.cauLeftSpr[4], sumValue));
            }
        });

        this.listLabelCau.forEach((label, index) => {
            label.string = `${this.LABEL_CAU[index]}: ${listCau[index]}`;
        });
    }

    private moveChipToDealer(chip: Node) {
        tween(chip)
            .to(0.5, { position: this.dealerNode.position })
            .call(() => chip.destroy())
            .start();
    }

    private moveChipToPlayer(chip: Node, player: CasinoPlayer, moveFromDealerFirst: boolean = false) {
        const targetPos = player.node.position.clone();
        const middlePos = chip.position.clone();

        if (moveFromDealerFirst) {
            const delayStep = 0.05;
            for (let i = 0; i < 10; i++) {
                const cloneChip = instantiate(chip);
                cloneChip.position = this.dealerNode.position;
                this.chipContainer.addChild(cloneChip);

                tween(cloneChip)
                    .delay(i * delayStep)
                    .to(0.3, { position: middlePos })
                    .call(() => cloneChip.destroy())
                    .start();
            }

            const totalDelay = 10 * delayStep;
            tween(chip)
                .delay(totalDelay + 0.1)
                .to(0.4, { position: targetPos })
                .call(() => chip.destroy())
                .start();

            return;
        }

        tween(chip)
            .to(0.5, { position: targetPos })
            .call(() => chip.destroy())
            .start();
    }

    private moveCardFromMachine(card: Node, cardIndex: number = -1, isFip: boolean = false, showBankerCorner: boolean = false) {
        const worldPos = this.baccaratMachine.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
        const startPos = card.parent.getComponent(UITransform).convertToNodeSpaceAR(worldPos);
        const endPos = card.position.clone();
        card.getComponent(Sprite).spriteFrame = this.cardBack;
        card.position = startPos;
        card.active = true;

        tween(card)
            .to(0.5, {position: endPos})
            .call(() => {
                if (cardIndex !== -1) {
                    if (isFip) {
                        this.flipCard(card, cardIndex);
                        return;
                    }

                    this.playerCorner.active = true;
                    if (showBankerCorner) {
                        this.bankerCorner.active = true;
                    }
                    card.getComponent(Sprite).spriteFrame = this.cardFronts[cardIndex];
                }
            })
            .start();
    }

    private flipCard(card: Node, cardIndex: number, isBanker: boolean = false) {
        tween(card)
            .to(0.2, {scale: v3(0, 0.65, 0.65)})
            .call(() => {
                card.getComponent(Sprite).spriteFrame = this.cardFronts[cardIndex];
            })
            .to(0.3, {scale: v3(0.65, 0.65, 0.65)})
            .call(() => {
                if (isBanker) {
                    this.bankerCardSum.getComponentInChildren(Label).string = this.getCardValue(cardIndex) + '';
                } else {
                    this.playerCardSum.getComponentInChildren(Label).string = this.getCardValue(cardIndex) + '';
                }
            })
            .start();
    }

    private getCardValue(id: number): number {
        if (id < 0 || id > 51) return -1;

        const cardNumber = id % 13;

        if (cardNumber === 12) return 1;
        if (cardNumber >= 0 && cardNumber <= 7) return cardNumber + 2;
        return 0;
    }

    private flipCorner(corner: Node, sum: number, isBanker: boolean = false) {
        var flip = corner.getChildByName('flip');
        tween(flip)
            .to(1.2, { x: 30 })
            .to(0.3, { x: 85 })
            .call(() => {
                corner.active = false;
                flip.x = 0;
                if (isBanker) {
                    this.bankerCardSum.getComponentInChildren(Label).string = sum + '';
                    return;
                }

                this.playerCardSum.getComponentInChildren(Label).string = sum + '';
            })
            .start();
    }

    showGuide() {
        var table = this.guideBG.children[0];
        table.children.forEach(child => {
            if (child.getChildByName("VIP") == null || child.getChildByName("NORMAL") == null) return;
            child.getChildByName("VIP").active = this.currency == 1;
            child.getChildByName("NORMAL").active = this.currency == 0;
        });
        this.guideBG.active = true;
        this.guide.active = true;
    }

    hideGuide() {
        this.guideBG.active = false;
        this.guide.active = false;
    }
}

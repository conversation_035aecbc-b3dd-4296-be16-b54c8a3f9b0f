[{"__type__": "cc.Prefab", "_name": "PopupGuide", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false, "persistent": false}, {"__type__": "cc.Node", "_name": "PopupGuide", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 10}, {"__id__": 65}], "_active": true, "_components": [{"__id__": 76}, {"__id__": 78}, {"__id__": 80}, {"__id__": 82}, {"__id__": 84}], "_prefab": {"__id__": 86}, "_lpos": {"__type__": "cc.Vec3", "x": 960, "y": 540, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfe+egpN5GuoVMYLyVvASK"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_opacity": 128}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5Bd3xxR9P6pQHPK5/78zM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bB2IMMNBDqrxjataXs+z5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fdDQnKV6ZNjImu1Po9QjXy"}, {"__type__": "cc.Node", "_name": "Container", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 11}, {"__id__": 21}], "_active": true, "_components": [{"__id__": 58}, {"__id__": 60}, {"__id__": 62}], "_prefab": {"__id__": 64}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Time", "_objFlags": 0, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 12}, {"__id__": 14}, {"__id__": 16}, {"__id__": 18}], "_prefab": {"__id__": 20}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 401.943, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 13}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "THỜI GIAN", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2eFiChml1Czbb0JzXxzYw1"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "id": "BTTON_GUIDE", "isUpperCase": false, "_id": "", "__prefab": {"__id__": 15}}, {"__type__": "cc.CompPrefabInfo", "fileId": "25uSySozVGCanZnnkUQTbK"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 17}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "6ftdch4spK5766ymQkvk/C"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 19}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 188.7, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bvSC/8jRAGLvm5n+8kFyL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0a/WsAK+5FY5vjDzIT36Ew"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 10}, "_children": [{"__id__": 22}], "_active": true, "_components": [{"__id__": 48}, {"__id__": 50}, {"__id__": 53}, {"__id__": 55}], "_prefab": {"__id__": 57}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -57.90799999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 512, "_parent": {"__id__": 21}, "_children": [{"__id__": 23}], "_active": true, "_components": [{"__id__": 41}, {"__id__": 43}, {"__id__": 45}], "_prefab": {"__id__": 47}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 512, "_parent": {"__id__": 22}, "_children": [{"__id__": 24}], "_active": true, "_components": [{"__id__": 34}, {"__id__": 36}, {"__id__": 38}], "_prefab": {"__id__": 40}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 115.30999755859375, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Time", "_objFlags": 0, "_parent": {"__id__": 23}, "_children": [], "_active": true, "_components": [{"__id__": 25}, {"__id__": 27}, {"__id__": 29}, {"__id__": 31}], "_prefab": {"__id__": 33}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "__prefab": {"__id__": 26}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 3, "_enableWrapText": true, "_font": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cc33DsM+xK0rO+fAQZ+Ecg"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "id": "txt_guide_baucua", "isUpperCase": false, "_id": "", "__prefab": {"__id__": 28}}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfyId7t1tK4qswnBPQpLj0"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "__prefab": {"__id__": 30}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "5dv/JTqOdGDrwkrE5lJ4lk"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "__prefab": {"__id__": 32}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1271, "height": 530.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}}, {"__type__": "cc.CompPrefabInfo", "fileId": "ebz7bh/wNAKZQmeKFUNYp/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "141FSnHVhEZ6zopz2Khydk"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "__prefab": {"__id__": 35}, "_resizeMode": 1, "_N$layoutType": 0, "_N$padding": 0, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 10, "_verticalDirection": 1, "_horizontalDirection": 0, "_affectedByScale": false, "_layoutSize": {"__type__": "cc.Size", "width": 1271, "height": 530.4}, "_layoutType": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "49Gro5sTZKUogFLQ+0Z5//"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "__prefab": {"__id__": 37}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "33ENeZq3NE6qzBzj6nNp7J"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "__prefab": {"__id__": 39}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1271, "height": 530.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c4OUUNJAdD2KF9Ncp53U2k"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7cspkPiTVGboXJGWm1XcKh"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "__prefab": {"__id__": 42}, "_materials": [], "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78o5fACPZK+6lDNt+RJlaf"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "__prefab": {"__id__": 44}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "38jhcKULRIWrbOCQmi3rTA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "__prefab": {"__id__": 46}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1271, "height": 700}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "5abOCWJIhF7IOGXs8LomlP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e0dAbZKmhHM54H9X89Cn2C"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": false, "__prefab": {"__id__": 49}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b730527c-3233-41c2-aaf7-7cdab58f9749@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48CDzGqIZHILz00q33tvkN"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 51}, "bounceDuration": 0.23, "brake": 0.75, "elastic": true, "inertia": true, "horizontal": false, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [{"__id__": 52}], "_content": {"__id__": 23}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4xVy5iPxAF5gAojN/h6fR"}, {"__type__": "cc.ClickEvent", "target": null, "component": "", "_componentId": "c5aeaULcr1B6Kb50KQNLQjR", "handler": "scrollEvent", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 54}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4+DiiZ3NC3YFKW6vj9XZE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 56}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1271, "height": 700}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "99MfVpethLd4ZHlCVwoP+p"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6de1eN6YxDdqacVzzCyuuk"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 59}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dbdbf5df-99ca-4366-ade6-eecd436f94bc@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4iCPD84pNiZz/zDEPcvvz"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 61}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "89hQkfICxGHa543I8dWLe2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 63}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1361, "height": 955}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "45b1HuXbVFP7KWVm71I6SQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dcdk3pstlNsp4XI7F2msxn"}, {"__type__": "cc.Node", "_name": "BtnClose", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 66}, {"__id__": 68}, {"__id__": 71}, {"__id__": 73}], "_prefab": {"__id__": 75}, "_lpos": {"__type__": "cc.Vec3", "x": 587.903, "y": 402.189, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "__prefab": {"__id__": 67}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7a782e51-5907-4722-b5aa-e93a8b6e57cc@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "75mhU6PQxDmYGxmg9RO+G9"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "__prefab": {"__id__": 69}, "clickEvents": [{"__id__": 70}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 65}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8b/OUPPJVKnaHvJkbbQmZy"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "89938zvBc1CAaNyePwL3b4R", "handler": "dismiss", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "__prefab": {"__id__": 72}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "684OQ2K7pCDYW/NPxdbkZe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "__prefab": {"__id__": 74}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 162, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "9cxjhX6OtE/oRbjheKzVE3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b3iDCBpoxG2rGGe6mTqXoH"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 77}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "096psMQ9RJ5LzR3FIHZ20I"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 79}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15MWicE0JBz4LwNms/OEwy"}, {"__type__": "89938zvBc1CAaNyePwL3b4R", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "bg": {"__id__": 2}, "container": {"__id__": 10}, "_id": "", "__prefab": {"__id__": 81}}, {"__type__": "cc.CompPrefabInfo", "fileId": "aaBFKIOD1Pvonwj50bwLN0"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 83}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "57ruAOZ9hA4ItQ1M1Xa2u7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 85}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "774a7+VjtPcaUqlPl7giU/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f1EuIweYBG0ZxJo1/WkECE"}]
import { _decorator, Node, Label, Sprite, SpriteFrame, instantiate, tween, Tween, v3, UIOpacity, UITransform } from 'cc';
import CasinoPlayer from "db://assets/Lobby/scripts/common/casino/Casino.Player";
import BlackJackPlay from "db://assets/BlackJack/BlackJackScript/BlackJack.Play";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu("BlackJack/Player")
export default class BlackJackPlayer extends CasinoPlayer {
    static instance: BlackJackPlayer = null;

    STATUS = {
        Bust: 0,
        DoubleDown: 1,
        Stand: 2,
        Split: 3,
        Hit: 4,
    }

    RESULT_STATUS = {
        WIN: 0,
        LOSE: 1,
        DRAW: 2,
        BLACKJACK: 3,
        BUST: 4,
    }

    start () {
        BlackJackPlayer.instance = this;
    }

    @property(Node)
    statusNode: Node = null;
    @property(Node)
    betAmount: Node = null;
    @property([Node])
    status: Node[] = [];
    @property(Node)
    mainCardContainer: Node = null;
    @property(Node)
    mainCards: Node = null;
    @property(Node)
    mainCard: Node = null;
    @property(Label)
    lblMainSum: Label = null;
    @property([Node])
    resultStatus: Node[] = [];
    @property(Node)
    effectAvatarWin: Node = null;
    @property(Node)
    splitCardContainer: Node = null;
    @property(Node)
    splitCards: Node = null;
    @property(Node)
    splitCard: Node = null;
    @property(Label)
    lblSplitSum: Label = null;
    @property(Sprite)
    progressSprite: Sprite = null;
    @property(SpriteFrame)
    progressSpriteGreen: SpriteFrame = null;
    @property(SpriteFrame)
    progressSpriteYellow: SpriteFrame = null;
    @property(SpriteFrame)
    progressSpriteRed: SpriteFrame = null;

    public totalBetAmount: number = 0;
    private lastMainCardIdx: number = 0;
    private lastSplitCardIdx: number = 0;
    public betLogs: number[] = [];
    public meTurn = false;
    public isShowedTurnMain = false;
    private hasSplit = false;

    leave() {
        super.leave();
        this.resetTable();
    }

    set(data: any) {
        super.set(data);
        this.resetTable();
    }

    resetTable() {
        this.totalBetAmount = 0;
        this.isWin = false;
        this.betLogs = [];
        this.mainCardContainer.active = false;
        this.splitCardContainer.active = false;
        this.statusNode.getChildByName('checkmark').active = false;
        this.betAmount.active = false;
        this.effectAvatarWin.active = false;
        for (let i = 0; i < this.resultStatus.length; i++) {
            this.resultStatus[i].active = false;
        }
        for (let i = 0; i < this.status.length; i++) {
            this.status[i].active = false;
        }
        this.hideReady();
        this.hideRegisterQuit();
        this.hideWinAnimation();
        this.progressSprite.node.active = false;
        this.hideTurnMain();
        this.hideTurnSplit();
        this.meTurn = false;
        this.hasSplit = false;
        this.isShowedTurnMain = false;
    }

    handleCardMain(cards: number[], sum: number[], bestSum: number = 0, isUpdate: boolean = false) {
        this.showTurnMain();
        this.handleCards(
            this.mainCardContainer,
            this.mainCards,
            this.lblMainSum,
            this.mainCard,
            cards,
            sum,
            bestSum,
            isUpdate,
            false
        );
    }

    handleCardSplit(cards: number[], sum: number[], bestSum: number = 0, isUpdate: boolean = false) {
        this.showTurnSplit();
        this.hasSplit = true;
        this.handleCards(
            this.splitCardContainer,
            this.splitCards,
            this.lblSplitSum,
            this.splitCard,
            cards,
            sum,
            bestSum,
            isUpdate,
            true
        );
    }

    private handleCards(
        container: Node,
        cardsNode: Node,
        label: Label,
        prefab: Node,
        cards: number[],
        sum: number[],
        bestSum: number = 0,
        isUpdate: boolean = false,
        isSplit: boolean = false
    ) {
        if (!isUpdate) {
            container.active = true;
            cardsNode.removeAllChildren();
            label.node.parent.active = false;
            label.string = "";
        }

        const lastIdxProp = isSplit ? 'lastSplitCardIdx' : 'lastMainCardIdx';
        const fromIdx = isUpdate ? this[lastIdxProp] : 0;

        for (let i = fromIdx; i < cards.length; i++) {
            const cardNode = instantiate(prefab);
            cardNode.getChildByName('arrow').active = false;
            cardNode.parent = cardsNode;
            cardNode.active = false;

            this.scheduleOnce(() => {
                BlackJackPlay.instance.moveCardFromMachine(cardNode, cards[i], this, isSplit);
            }, i - fromIdx);
        }

        this.scheduleOnce(() => {
            if (isUpdate) {
                label.string = bestSum.toString();
                if (bestSum > 21) {
                    this.updateStatus(this.STATUS.Bust);
                    cardsNode.children.forEach(cardNode => {
                        cardNode.getChildByName('mask').active = true;
                    });
                }

                if (bestSum >= 21 && this.id === BlackJackPlay.instance.mePlayer.id && (!this.hasSplit || isSplit) ) {
                    this.resetTimeout();
                    BlackJackPlay.instance.inGameButtons.forEach(button => {
                        button.getChildByName('disable').active = true;
                    });
                }
            } else {
                label.node.parent.active = true;
                label.string = sum.join("/");
                if (sum.includes(21)) {
                    this.showResult(BlackJackPlayer.instance.RESULT_STATUS.BLACKJACK);
                }
            }
        }, cards.length - fromIdx);

        this[lastIdxProp] = cards.length;
    }


    updateBetAmount(coin: number) {
        this.betLogs.push(coin);
        this.totalBetAmount += coin;
        this.betAmount.active = true;
        this.betAmount.getComponentInChildren(Label).string = Utils.formatMoney(this.totalBetAmount, true);
    }

    clearBetAmount() {
        this.totalBetAmount = 0;
        this.betLogs = [];
        this.betAmount.active = false;
        this.betAmount.getComponentInChildren(Label).string = "0";
    }

    updateStatus(status: number) {
        for (let i = 0; i < this.status.length; i++) {
            this.status[i].active = i === status;
        }
    }

    showResult(status: number) {
        for (let i = 0; i < this.resultStatus.length; i++) {
            this.resultStatus[i].active = i === status;
        }

        for (let i = 0; i < this.status.length; i++) {
            this.status[i].active = false;
        }

        if (status === this.RESULT_STATUS.WIN) {
            this.effectAvatarWin.active = true;
        }
    }

    resetTimeout() {
        this.unschedule(this.playerTimeoutFunction);
        this.progressSprite.node.active = false;
        this.progressSprite.fillRange = 0;
        this.progressSprite.spriteFrame = this.progressSpriteGreen;
        this.progressSprite.node.setScale(v3(-1, 1, 1));
        this.hideTurnMain();
        this.hideTurnSplit();
    }

    private playerTimeoutFunction: Function = null;
    updateTimeout(timeout: number) {
        this.progressSprite.node.active = true;
        const totalTime = timeout;
        let elapsed = 0;

        this.schedule(this.playerTimeoutFunction = () => {
            timeout -= 0.05;
            if (timeout <= 0) {
                this.unschedule(this.playerTimeoutFunction);
                this.progressSprite.node.active = false;
                if (this.id === BlackJackPlay.instance.mePlayer.id) {
                    BlackJackPlay.instance.inGameButtons.forEach(button => {
                        button.getChildByName('disable').active = true;
                    });
                }
                return;
            }
            elapsed += 0.05;
            const percent = Math.min(elapsed / totalTime, 1);
            this.progressSprite.fillRange = percent;
            this.progressSprite.spriteFrame = percent < 0.5 ? this.progressSpriteGreen : (percent < 0.8 ? this.progressSpriteYellow : this.progressSpriteRed);
            this.progressSprite.node.setScale(v3(-1, 1, 1));
        }, 0.05);
    }

    blinkSprite(spriteNode: Node) {
        if (!spriteNode) return;

        var spriteOpacity = spriteNode.getComponent(UIOpacity);
        spriteOpacity.opacity = 255;
        Tween.stopAllByTarget(spriteNode);
        tween(spriteOpacity)
            .repeatForever(
                tween()
                    .to(0.4, { opacity: 100 })
                    .to(0.4, { opacity: 255 })
            )
            .start();
    }

    stopBlinkSprite(spriteNode: Node) {
        if (!spriteNode) return;

        Tween.stopAllByTarget(spriteNode);
        spriteNode.getComponent(UIOpacity).opacity = 255;
    }

    showTurnMain() {
        if (!this.meTurn) return;
        this.isShowedTurnMain = true;
        this.mainCards.children.forEach((cardNode, index) => {
            const turnSprite = cardNode.getChildByName('turn');
            const sprite = turnSprite?.getChildByName('sprite');
            if (sprite) {
                sprite.getComponent(UITransform).anchorX = index === 0 ? 0.5 : 0;
                turnSprite.active = true;
                this.blinkSprite(sprite);
            }

            cardNode.getChildByName('arrow').active = index === 0;
        });
    }

    hideTurnMain() {
        this.mainCards.children.forEach(cardNode => {
            const turnSprite = cardNode.getChildByName('turn');
            const sprite = turnSprite?.getChildByName('sprite');
            if (sprite) {
                this.stopBlinkSprite(sprite);
            }
            if (turnSprite) {
                turnSprite.active = false;
            }

            cardNode.getChildByName('arrow').active = false;
        });
    }

    showTurnSplit() {
        if (!this.meTurn) return;

        this.splitCards.children.forEach((cardNode, index) => {
            const turnSprite = cardNode.getChildByName('turn');
            const sprite = turnSprite?.getChildByName('sprite');
            if (sprite) {
                sprite.getComponent(UITransform).anchorX = index === 0 ? 0.5 : 0;
                turnSprite.active = true;
                this.blinkSprite(sprite);
            }

            cardNode.getChildByName('arrow').active = index === 0;
        });
    }

    hideTurnSplit() {
        this.splitCards.children.forEach(cardNode => {
            const turnSprite = cardNode.getChildByName('turn');
            const sprite = turnSprite?.getChildByName('sprite');
            if (sprite) {
                this.stopBlinkSprite(sprite);
            }
            if (turnSprite) {
                turnSprite.active = false;
            }

            cardNode.getChildByName('arrow').active = false;
        });
    }
}

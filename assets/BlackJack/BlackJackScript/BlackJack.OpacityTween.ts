import { _decorator, Component, tween, UIOpacity } from "cc";
const { ccclass, menu } = _decorator;

@ccclass
@menu("BlackJack/OpacityTween")
export default class OpacityTween extends Component {
    start() {
        const opacityComp = this.node.getComponent(UIOpacity) || this.node.addComponent(UIOpacity);
        opacityComp.opacity = 255;

        tween(opacityComp)
            .repeatForever(
                tween()
                    .to(0.3, { opacity: 255 })
                    .to(0.3, { opacity: 120 })
            )
            .start();
    }
}
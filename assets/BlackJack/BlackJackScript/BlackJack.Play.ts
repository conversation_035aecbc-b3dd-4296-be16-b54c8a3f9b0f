import { _decorator, Component, Node, Label, Sprite, SpriteFrame, Prefab, instantiate, Animation, EditBox, Color, Layout, Vec3, sys, v3, tween, Tween, UIOpacity, UITransform } from 'cc';
import BlackJackPlayer from './BlackJack.Player';
import App from '../../Lobby/scripts/common/App';
import BlackjackSignalRClient from "db://assets/Lobby/scripts/common/networks/BlackjackSignalRClient";
import CasinoLobby from "db://assets/Lobby/scripts/common/casino/Casino.Lobby";
import Configs from '../../Lobby/scripts/common/Config';
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import BundleControl from "db://assets/Loading/scripts/BundleControl";
import CasinoPopupRank from "db://assets/Lobby/scripts/common/casino/Casino.PopupRank";

const { ccclass, property, menu } = _decorator;

enum PHASE {
    None = 0,
    Betting = 1,
    Dealing = 2,
    InsurancePhrase = 3,
    PlayerActions = 4,
    DealerActions = 5,
    Result = 6,
}

enum ACTION {
    None = 0,
    Bet = 1,
    Stand = 2,
    Hit = 3,
    DoubleDown = 4,
    Split = 5,
    Surrender = 6,
    Insurance = 7,
    EvenMoney = 8,
    Clear = 9
}

@ccclass
@menu("BlackJack/Play")
export default class BlackJackPlay extends Component {
    static instance: BlackJackPlay = null;

    @property(Node)
    roomDetail: Node = null;
    @property([SpriteFrame])
    listTableSpr: SpriteFrame[] = [];
    @property(Sprite)
    tableSpr: Sprite = null;
    @property(Node)
    chipNodes: Node = null;
    @property(Node)
    boxSettingContainer: Node = null;

    // JOIN ROOM
    @property(Label)
    lblToast: Label = null;
    @property([BlackJackPlayer])
    players: BlackJackPlayer[] = [];
    @property(BlackJackPlayer)
    mePlayer: BlackJackPlayer = null;
    @property(Label)
    lblDeck: Label = null;

    // BET
    @property(Node)
    blackjackMachine: Node = null;
    @property(SpriteFrame)
    cardBack: SpriteFrame = null
    @property([SpriteFrame])
    cardFronts: SpriteFrame[] = [];
    @property(Node)
    chipContainer: Node = null;
    @property(Label)
    countdownSecond: Label = null;
    @property(Sprite)
    progressSprite: Sprite = null;
    @property(SpriteFrame)
    progressSpriteGreen: SpriteFrame = null;
    @property(SpriteFrame)
    progressSpriteYellow: SpriteFrame = null;
    @property(SpriteFrame)
    progressSpriteRed: SpriteFrame = null;
    amounts = [];
    amounts_1 = [
        1,
        5,
        10,
        50,
        100
    ];
    amounts_2 = [
        1,
        2,
        10,
        20,
        100
    ]

    minBet: number = 0;
    amountSelected: number = 0;
    betLogs: any[] = [];
    flagLog: number = -1;
    flagIsPhaseBetting: boolean = false;

    meBetPosition: number = 0;
    sessionId: number = 0;
    roomId: number;
    roomValue: number;
    currency: number;

    @property(Node)
    boxChat: Node = null;
    @property(Prefab)
    popupRank: Prefab = null;
    @property(Node)
    popupContainer: Node = null;

    // SEND MESSAGE
    @property(EditBox)
    editBoxChat: EditBox = null;

    // DEALER
    @property(Node)
    dealerNode: Node = null;
    @property(Node)
    dealerCardContainer: Node = null;
    @property(Node)
    dealerCards: Node = null;
    @property(Node)
    dealerCard: Node = null;
    @property(Label)
    lblDealerSum: Label = null;
    @property(Node)
    dealerCardBlackjack: Node = null;
    lastCardIdx: number = 0;

    //BOX DEALER
    @property(Label)
    labelDealerNotify: Label = null;

    // BUTTON IN_GAME
    @property([Node])
    inGameButtons: Node[] = [];
    @property(Node)
    bottomBarBet: Node = null;
    @property(Node)
    nodeDisableRebet: Node = null;
    @property(Node)
    nodeDisableBetX2: Node = null;
    @property(Node)
    nodeDisableClearBet: Node = null;
    @property(Node)
    nodeDisableFinishBet: Node = null;
    @property(Node)
    bottomBarDeal: Node = null;
    @property(Node)
    nodeInsurance: Node = null;
    @property(Node)
    nodeEvenMoney: Node = null;

    //Help
    @property(Node)
    boxHelp: Node = null;

    //TABLE VIP NORMAL
    @property(SpriteFrame)
    spriteFrameBetVip: SpriteFrame = null;
    @property(SpriteFrame)
    spriteFrameBetNormal: SpriteFrame = null;
    @property(SpriteFrame)
    spriteFrameActiveBetVip: SpriteFrame = null;
    @property(SpriteFrame)
    spriteFrameActiveBetNormal: SpriteFrame = null;
    @property([Node])
    arrayNodesBet: Node[] = [];

    @property(Node)
    guideBG: Node = null;
    @property(Node)
    guide: Node = null;

    init(roomValue: number, currency: number) {
        this.roomValue = roomValue;
        this.currency = currency;
    }

    protected start() {
        BlackJackPlay.instance = this;
        App.instance.showLoading(true);
        BlackjackSignalRClient.getInstance().send('EnterRoom', [this.roomValue, this.currency], (data) => {
            App.instance.showLoading(false);
            if (data.c < 0) {
                App.instance.alertDialog.showMsg(BlackjackSignalRClient.getErrMsg(data.c));
                this.node.destroy();
                return;
            }

            this.joinRoom(data.r);
            if (sys.localStorage.getItem("CA_BJ_first_time") === null) {
                sys.localStorage.setItem("CA_BJ_first_time", "1");
                this.showGuide();
            }
        });

        const isVip = CasinoLobby.instance.isTableVip;
        const [frameBet, frameActive] = isVip
            ? [this.spriteFrameBetVip, this.spriteFrameActiveBetVip]
            : [this.spriteFrameBetNormal, this.spriteFrameActiveBetNormal];

        this.arrayNodesBet.forEach(node => {
            const spriteBet = node.getChildByName("Status");
            const spriteBetActive = spriteBet.getChildByName("checkmark");

            spriteBet.getComponent(Sprite).spriteFrame = frameBet;
            spriteBetActive.getComponent(Sprite).spriteFrame = frameActive;
        });
        this.flagLog = -1;
    }

    onLoad() {
        this.chipNodes.children.forEach((button, index) => {
            const chip = button.getChildByName("chip");
            const text = button.getChildByName("text");

            chip.on(Node.EventType.TOUCH_END, () => {
                const isSelected = chip["_isSelected"];

                if (isSelected) {
                    Tween.stopAllByTarget(chip);
                    chip.y = 0;
                    text.getComponent(Label).color = new Color().fromHEX("#FFFFFF");
                    chip["_isSelected"] = false;
                    return;
                }

                this.chipNodes.children.forEach((otherButton) => {
                    const otherChip = otherButton.getChildByName("chip");
                    const otherText = otherButton.getChildByName("text");
                    Tween.stopAllByTarget(otherChip);
                    otherChip.y = 0;
                    otherText.getComponent(Label).color = new Color().fromHEX("#FFFFFF");
                    otherChip["_isSelected"] = false;
                });

                text.getComponent(Label).color = new Color().fromHEX("#FFF000");
                chip["_isSelected"] = true;
                this.amountSelected = this.amounts[index] * this.minBet;

                tween(chip)
                    .repeatForever(
                        tween()
                            .to(0.3, { y: 15 })
                            .to(0.3, { y: 0 })
                    )
                    .start();
            });
        });

        this.mePlayer.statusNode.on(Node.EventType.TOUCH_END, () => {
            if (this.amountSelected == 0) {
                return;
            }

            this.actBet(this.amountSelected);
        });

        this.inGameButtons.forEach(button => {
            button.on(Node.EventType.TOUCH_END, () => {
                if (button.getChildByName('disable').active) {
                    return;
                }

                this.sendRequestAction(ACTION[button.name]);
            });
        });

        BlackjackSignalRClient.getInstance().receive('roomData', (data) => {
            if (data.c < 0) {
                this.showToast(BlackjackSignalRClient.getErrMsg(data.c));
                return;
            }

            this.sessionId = data.r.SessionId;
            this.roomDetail.getChildByName('RoomSession').getComponent(Label).string = App.instance.getTextLang('txt_session') + `: #${this.sessionId}`;
            this.lblDeck.string = data.r.Deck;
            this.handleSession(data.r.Session);
            data.r.Players.forEach((player: any) => {
               var playerObj = this.getAllPlayersById(player.AccountId);
                if (playerObj) {
                     playerObj.setCoin(player.Balance);
                }
            });
        });

        BlackjackSignalRClient.getInstance().receive('joinRoom', (data) => {
            if (data.c < 0) {
                this.showToast(BlackjackSignalRClient.getErrMsg(data.c));
                return;
            }

            if (data.r.AccountId != `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                var playerRFP = this.randomFreePlayer();
                playerRFP.set(data.r);
                if (this.flagIsPhaseBetting) {
                    playerRFP.statusNode.getChildByName('checkmark').active = false;
                }
            }
        });

        BlackjackSignalRClient.getInstance().receiveArray('recieveMessage', (accountId: string, _nickname: string, content: string) => {
            var playerRM = this.getAllPlayersById(accountId);
            playerRM.showChatMsg(content);
        });

        BlackjackSignalRClient.getInstance().receive('leaveRoom', (data) => {
            if (data.c < 0) {
                this.showToast(BlackjackSignalRClient.getErrMsg(data.c));
                return;
            }

            for (var i = 0; i < data.r.length; i++) {
                var item = data.r[i];
                if (item.id == `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                    if (item.reason < 0) {
                        App.instance.alertDialog.showMsg(App.instance.getTextLang(`ca${item.reason}`));
                    }
                    BlackjackSignalRClient.getInstance().dontReceive();
                    App.instance.gotoLobby();
                } else {
                    this.getPlayerById(item.id).leave();
                }
            }
        })

        BlackjackSignalRClient.getInstance().receive('registerLeavingRoom', (data) => {
            var playerRLR = this.getAllPlayersById(data.r.id);
            if (data.r.status) {
                playerRLR.showRegisterQuit();
            } else {
                playerRLR.hideRegisterQuit();
            }
        });

        BlackjackSignalRClient.getInstance().receive('rejectBetting', (data) => {
            let rejectedPlayers = data.map((id: string) => this.getPlayerById(id).lblNickname.string).join(", ");
            this.showToast(`Reject betting from ${rejectedPlayers}`);
        });

        BlackjackSignalRClient.getInstance().receive('clearBetting', (data) => {
            var playerCBId = data.r.id;
            var playerCB = this.getPlayerById(playerCBId);
            this.chipContainer.children.filter(child => child.name === playerCBId).forEach(chip => {
                this.moveChipToPlayer(chip, this.mePlayer);
            });

            playerCB.setCoin(data.r.balance);
            playerCB.clearBetAmount();
        });

        BlackjackSignalRClient.getInstance().receive('confirmBetting', (data) => {
            var playerCB = this.getAllPlayersById(data.r.id);
            playerCB.showReady();
        });

        BlackjackSignalRClient.getInstance().receive('playerOtherDevice', (data) => {
            this.showToast(BlackjackSignalRClient.getErrMsg(data));
        });

        BlackjackSignalRClient.getInstance().receive('connectionChanged', (data) => {
            var playerCC = this.getPlayerById(data.r.id);
            this.showToast(`${playerCC.lblNickname.string} ${data.status ? "connected" : "disconnected"}`);

            if (data.status) {
                playerCC.leave();
            }
        });

        BlackjackSignalRClient.getInstance().receive('playerBet', (data) => {
            if (data.c < 0) {
                this.showToast(BlackjackSignalRClient.getErrMsg(data.c));
                return;
            }

            var player = this.getPlayerById(data.r.id);
            player.setCoin(data.r.balance);
            this.placeBet(player, data.r.amount);
        })

        BlackjackSignalRClient.getInstance().receive('playerAction', (data) => {
            this.handlePlayerAction(data.r);
        });

        BlackjackSignalRClient.getInstance().receive('updateHand', (data) => {
            this.updateHandDealer(data.Cards, data.BestSum);
        });

        BlackjackSignalRClient.getInstance().receive('updatePrizes', (data) => {
            this.handleResultPrizes(data)
        });
    }

    sendRequestAction(action: ACTION, confirmed: boolean = true) {
        BlackjackSignalRClient.getInstance().send('Request', [action, confirmed], (data) => {
            if (data.c < 0) {
                this.showToast(BlackjackSignalRClient.getErrMsg(data.c));
                return;
            }

            this.handlePlayerAction(data.r);
        });
    }

    sendRequestActionInsurance(_event: Event, data: string) {
        this.sendRequestAction(ACTION.Insurance, data == "true");
        this.nodeInsurance.active = false;
    }

    sendRequestActionEvenMoney(_event: Event, data: string) {
        this.sendRequestAction(ACTION.EvenMoney, data == "true");
        this.nodeEvenMoney.active = false;
    }

    handlePlayerAction(data: any) {
        var playerPA = this.getAllPlayersById(data.accountId);
        [this.mePlayer, ...this.players].forEach(player => {
            player.meTurn = false;
            player.hideTurnSplit();
            player.hideTurnMain();
        });
        playerPA.meTurn = true;
        switch (data.action) {
            case ACTION.Hit:
            case ACTION.Stand:
            case ACTION.DoubleDown:
                var handNormal= data.hand;
                if (handNormal.Index == 0) {
                    playerPA.handleCardMain(handNormal.Cards, handNormal.Sum, handNormal.BestSum, true);
                } else if (handNormal.Index == 1) {
                    playerPA.handleCardSplit(handNormal.Cards, handNormal.Sum, handNormal.BestSum, true);
                }
                if (data.action == ACTION.Hit) {
                    playerPA.updateStatus(BlackJackPlayer.instance.STATUS.Hit);
                }

                if (data.action == ACTION.Stand) {
                    playerPA.updateStatus(BlackJackPlayer.instance.STATUS.Stand);
                }

                if (data.action == ACTION.DoubleDown) {
                    playerPA.updateStatus(BlackJackPlayer.instance.STATUS.DoubleDown);
                    this.placeBet(playerPA, this.betLogs[0].amount);
                }
                break;
            case ACTION.Insurance:
                if (data.confirmed && data.accountId == `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                    this.showToast(App.instance.getTextLang('me0'));
                    var amount = this.mePlayer.totalBetAmount / 2;
                    var amountNode = this.mePlayer.createCoinNode(Utils.formatMoney(amount, true));
                    this.moveChipToDealer(amountNode)
                }
                break;
            case ACTION.EvenMoney:
                if (data.confirmed && data.accountId == `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                    this.showToast(App.instance.getTextLang('me0'));
                }
                break;
            case ACTION.Split:
                playerPA.updateStatus(BlackJackPlayer.instance.STATUS.Split);
                break;
            default:
                break;
        }
    }

    joinRoom(data: any) {
        this.roomId = data.Id;
        this.sessionId = data.SessionId;
        this.minBet = data.Value;
        this.roomDetail.getChildByName('RoomTable').getComponent(Label).string = (data.Currency == 0 ? App.instance.getTextLang('tb113') : App.instance.getTextLang('tb112')) + `: ${data.Id}`;
        this.roomDetail.getChildByName('RoomValue').getComponent(Label).string = App.instance.getTextLang('iap38') + ': ' + Utils.formatMoney(data.Value) + ' ' + (data.Currency == 0 ? App.instance.getTextLang('txt_coin') : 'Tipzo');
        this.roomDetail.getChildByName('RoomSession').getComponent(Label).string = App.instance.getTextLang('txt_session') + `: #${data.SessionId}`;
        this.tableSpr.spriteFrame = this.listTableSpr[data.Currency];
        this.lblDeck.string = data.Deck;

        if ([500, 5000, 50000].includes(this.minBet)) {
            this.amounts = this.amounts_2;
        } else {
            this.amounts = this.amounts_1;
        }

        this.chipNodes.children.forEach((button, index) => {
            const text = button.getChildByName("text");
            text.getComponent(Label).string = Utils.formatMoney(this.amounts[index] * this.minBet, true);
            if (index == 0) {
                button.getChildByName("chip").emit(Node.EventType.TOUCH_END);
            }
        });

        var players = data.Players;
        if (players && players.length > 0) {
            for (var i = 0; i < players.length; i++) {
                if (players[i].AccountId != `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                    this.randomFreePlayer().set(players[i]);
                } else {
                    this.mePlayer.set(players[i]);
                    this.meBetPosition = players[i].Position;
                }
            }
        }

        if (data.Session) {
            this.handleSession(data.Session, true);
        }
    }

    handleSession(session: any, isJoin = false) {
        this.unscheduleAllCallbacks();

        var dealerBET = this.dealerNode.getChildByName('BET');
        var dealerNormal = this.dealerNode.getChildByName('NORMAL');
        dealerNormal.active = true;
        dealerBET.active = false;
        this.labelDealerNotify.string = 'BLACKJACK';
        if (session.Phrase == PHASE.None) {
            dealerNormal.active = true;
            return;
        }

        [this.mePlayer, ...this.players].forEach(player => {
            player.resetTimeout();
        });

        this.inGameButtons.forEach(button => {
            button.getChildByName('disable').active = true;
        });

        this.countdownSecond.node.parent.active = session.Phrase == PHASE.Betting;
        if (session.Phrase == PHASE.Betting) {
            this.updateTimeout(session.Timeout);
        } else if (session.Phrase == PHASE.PlayerActions) {
            session.Roles?.forEach((rol: any) => {
                var playerPAR = this.getAllPlayersById(rol.AccountId);
                playerPAR.updateTimeout(session.Timeout);
            });
        }

        if (session.Timeout) {
            this.updateTimeout(session.Timeout);
        }

        [this.mePlayer, ...this.players].forEach(player => {
            player.statusNode.getChildByName('checkmark').active = false;
        });
        this.flagIsPhaseBetting = false;
        if (session.Phrase == PHASE.Betting) {
            this.flagIsPhaseBetting = true;
            this.flagLog++;
            this.betLogs = this.betLogs.filter(log => log.flag == this.flagLog - 1);

            this.nodeInsurance.active = false;
            this.nodeEvenMoney.active = false;
            [this.mePlayer, ...this.players].forEach(player => {
                player.resetTable();
            });
            this.dealerCardContainer.active = false;
            this.chipContainer.removeAllChildren();
            this.bottomBarDeal.active = false;
            this.bottomBarBet.active = true;
            this.nodeDisableRebet.active = this.betLogs.length == 0;
            this.nodeDisableBetX2.active = this.betLogs.length == 0;
            this.nodeDisableClearBet.active = true;
            this.nodeDisableFinishBet.active = true;

            [this.mePlayer, ...this.players].forEach(player => {
                player.statusNode.getChildByName('checkmark').active = player.id == this.mePlayer.id;
            });
            dealerNormal.active = false;
            dealerBET.active = true;
            dealerBET.getComponent(Animation).play();
            const currency = this.currency == 0 ? App.instance.getTextLang('txt_coin') : 'Tipzo';
            const messages = [
                App.instance.getTextLang('ca157'),
                App.instance.getTextLang('ca150') + ` ${Utils.formatNumber(this.minBet)} ${currency}`
            ];

            let indexMsg = 0;

            this.schedule(() => {
                const parentNode = this.labelDealerNotify.node.parent;
                const opacityComp = parentNode.getComponent(UIOpacity) || parentNode.addComponent(UIOpacity);

                tween(opacityComp)
                    .to(0.5, { opacity: 0 })
                    .call(() => {
                        this.labelDealerNotify.string = messages[indexMsg];
                        indexMsg++;
                        if (indexMsg >= messages.length) indexMsg = 0;
                    })
                    .to(0.2, { opacity: 255 })
                    .start();
            }, 2.5);
        } else {
            this.bottomBarBet.active = false;
            this.bottomBarDeal.active = true;
        }

        if (session.Phrase == PHASE.Dealing) {
            [this.mePlayer, ...this.players].forEach(player => {
                player.hideReady();
            });
            dealerNormal.active = true;
            this.labelDealerNotify.string = App.instance.getTextLang('ca110');

            this.setupHandDealer(session.DealerHand.Cards);
            this.handlePlayerBoxes(session.Boxes, isJoin);
            this.inGameButtons.forEach(button => {
                button.getChildByName('disable').active = true;
            });
        }

        if (session.Phrase == PHASE.InsurancePhrase || session.Phrase == PHASE.PlayerActions) {
            var meRole = session.Roles?.find((rol: any) => {
                return rol.AccountId == `${Configs.Login.AccountID}:${Configs.Login.PortalID}`;
            });

            session.Roles?.forEach((rol: any) => {
                var playerPA = this.getAllPlayersById(rol.AccountId);
                playerPA.meTurn = true;
                if (!playerPA.isShowedTurnMain) {
                    playerPA.showTurnMain();
                }
            });
        }

        if (session.Phrase == PHASE.InsurancePhrase) {
            this.labelDealerNotify.string = App.instance.getTextLang('ca160');
            if (meRole?.Actions.includes(ACTION.Insurance)) {
                this.nodeInsurance.active = true;
            }

            if (meRole?.Actions.includes(ACTION.EvenMoney)) {
                this.nodeEvenMoney.active = true;
            }
        }

        if (session.Phrase == PHASE.PlayerActions) {
            this.labelDealerNotify.string = App.instance.getTextLang('ca158');
            this.nodeInsurance.active = false;
            this.nodeEvenMoney.active = false;
            if (meRole) {
                this.inGameButtons.forEach(button => {
                    button.getChildByName('disable').active = !meRole.Actions.includes(ACTION[button.name]);
                });
            }

            if (isJoin) {
                this.handlePlayerBoxes(session.Boxes, true, false);
            } else {
                this.handlePlayerBoxes(session.Boxes, false, true);
            }
        }

        if (isJoin && session.Phrase > PHASE.Betting) {
            this.inGameButtons.forEach(button => {
                button.getChildByName('disable').active = true;
            });
            this.setupHandDealer([session.DealerHand.Cards[0], -1]);
            if (session.Phrase >= PHASE.DealerActions) {
                this.updateHandDealer(session.DealerHand.Cards, session.DealerHand.BestSum);
            }
        }

        if (session.Phrase == PHASE.DealerActions && !isJoin) {
            this.labelDealerNotify.string = App.instance.getTextLang('ca159');
            this.updateHandDealer(session.DealerHand.Cards, session.DealerHand.BestSum);
        }

        if (session.Phrase == PHASE.Result) {
            this.labelDealerNotify.string = App.instance.getTextLang('me18');
            dealerNormal.active = true;
            if (session.Prizes) {
                this.handleResultPrizes(session.Prizes);
            }

            if (session.EventPrizes) {
                session.EventPrizes.forEach((_prize: any) => {});
            }

            [this.mePlayer, ...this.players].filter(player => !player.isWin).forEach(player => {
                var isPlayerBet = false;
                this.chipContainer.children.filter(child => child.name === player.id).forEach(chip => {
                    isPlayerBet = true;
                    this.moveChipToDealer(chip, player);
                });

                this.scheduleOnce(() => {
                    if (isPlayerBet) {
                        player.showResult(BlackJackPlayer.instance.RESULT_STATUS.LOSE);
                    }
                }, 0.5);
            });
        }

        if (session.Phrase != PHASE.Dealing && session.Phrase != PHASE.PlayerActions && isJoin) {
            this.handlePlayerBoxes(session.Boxes, true);
        }
    }

    handlePlayerBoxes(boxes: any, isJoin: boolean = false, checkCaseSplitOnly: boolean = false) {
        boxes.forEach((box: any) => {
            const mainHand = box.Hands[0];
            const splitHand = box.Hands.length > 1 ? box.Hands[1] : null;
            const isValidSplitCase =
                box.Hands.length == 2 &&
                mainHand?.Cards.length === 1 &&
                splitHand?.Cards.length === 1;

            if (checkCaseSplitOnly && !isValidSplitCase) {
                return;
            }

            var playerB = this.getAllPlayersById(box.Owner);
            if (playerB) {
                if (mainHand.Cards) {
                    playerB.handleCardMain(mainHand.Cards, mainHand.Sum);
                }

                if (isJoin && mainHand.BettingLogs) {
                    mainHand.BettingLogs.forEach((log: any) => {
                        this.placeBet(playerB, log.Amount);
                    });
                }

                if (splitHand && splitHand.Cards) {
                    playerB.handleCardSplit(splitHand.Cards, splitHand.Sum);
                    if (isJoin && splitHand.BettingLogs) {
                        splitHand.BettingLogs.forEach((log: any) => {
                            this.placeBet(playerB, log.Amount);
                        });
                    }
                }
            }
        });
    }

    handleResultPrizes(prizes: any[]) {
        prizes.forEach((prize: any) => {
            const accountId = prize.AccountId || prize.accountId;
            const player = this.getAllPlayersById(accountId);
            if (!player) return;

            player.isWin = true;

            this.chipContainer.children
                .filter(child => child.name === player.id)
                .forEach(chip => this.moveChipToPlayer(chip, player, true));

            const totalPrize = prize.TotalPrize ?? prize.totalPrize ?? 0;
            const totalBet = prize.TotalBet ?? prize.totalBet ?? 0;
            const type = prize.Type ?? prize.type;

            switch (type) {
                case 1:
                    // player.showResult(BlackJackPlayer.instance.RESULT_STATUS.BLACKJACK);
                    player.showWinAnimation();
                    break;
                case 0:
                    if (totalPrize > totalBet) {
                        player.showResult(BlackJackPlayer.instance.RESULT_STATUS.WIN);
                        player.showWinAnimation();
                    } else if (totalPrize === totalBet) {
                        player.showResult(BlackJackPlayer.instance.RESULT_STATUS.DRAW);
                    }
                    break;
                default:
                    break;
            }
        });
    }

    updateTimeout(timeout: number) {
        const totalTime = timeout;
        let elapsed = 0;

        this.schedule(() => {
            elapsed += 0.05;
            const percent = Math.min(elapsed / totalTime, 1);
            this.progressSprite.fillRange = percent;
            this.progressSprite.spriteFrame = percent < 0.5 ? this.progressSpriteGreen : (percent < 0.8 ? this.progressSpriteYellow : this.progressSpriteRed);
            this.progressSprite.node.setScale(v3(-1, 1, 1));
        }, 0.05);

        this.countdownSecond.string = timeout < 10 ? `0${timeout}` : timeout + '';
        this.schedule(() => {
            if (timeout < 0) {
                this.unscheduleAllCallbacks();
                return;
            }
            this.countdownSecond.string = timeout < 10 ? `0${timeout}` : timeout + '';
            timeout--;
        }, 1);
    }

    randomFreePlayer() {
        for (var i = 0; i < this.players.length; i++) {
            if (this.players[i].id == "") {
                return this.players[i];
            }
        }

        return null;
    }

    getPlayerById(id: string) {
        for (var i = 0; i < this.players.length; i++) {
            if (this.players[i].id == id) {
                return this.players[i];
            }
        }

        return null;
    }

    getAllPlayersById(id: string) {
        return [this.mePlayer, ...this.players].find(player => player.id === id);
    }

    getChipByAmount(amount: number) {
        return this.chipNodes.children.find((_chip, index) => {
            return this.amounts[index] === amount / this.minBet;
        })?.getChildByName("chip");
    }

    toggleMenu() {
        this.boxSettingContainer.active = !this.boxSettingContainer.active;
    }

    clearBetting() {
        if (this.nodeDisableClearBet.active) {
            return;
        }

        BlackjackSignalRClient.getInstance().send('ClearBetting', [], (data) => {
            if (data.c < 0) {
                this.showToast(BlackjackSignalRClient.getErrMsg(data.c));
                return;
            }

            this.chipContainer.children.filter(child => child.name === this.mePlayer.id).forEach(chip => {
                this.moveChipToPlayer(chip, this.mePlayer);
            });
            this.nodeDisableClearBet.active = true;
            this.nodeDisableRebet.active = true;
            this.nodeDisableBetX2.active = true;
            this.nodeDisableFinishBet.active = true;
            this.betLogs = [];
            this.mePlayer.setCoin(data.r.balance);
            this.mePlayer.clearBetAmount();
        });
    }

    x2Betting() {
        if (this.nodeDisableBetX2.active) {
            return;
        }

        this.actBetFromBetLogs();
        setTimeout(() => {
            this.actBetFromBetLogs();
        }, 500);
    }

    reLastBet() {
        if (this.nodeDisableRebet.active) {
            return;
        }

        this.actBetFromBetLogs();
    }

    actBetFromBetLogs() {
        this.betLogs.forEach(log => {
            if (log.flag == this.flagLog - 1) {
                this.actBet(log.amount);
            }
        });
    }

    actBet(amount: number) {
        BlackjackSignalRClient.getInstance().send('Bet', [amount, this.meBetPosition], (data) => {
            if (data.c < 0) {
                this.showToast(BlackjackSignalRClient.getErrMsg(data.c));
                return;
            }

            this.nodeDisableClearBet.active = false;
            this.nodeDisableRebet.active = true;
            this.nodeDisableBetX2.active = true;
            this.nodeDisableFinishBet.active = false;

            var dataBet = data.r;
            this.placeBet(this.mePlayer, dataBet.amount);
            this.mePlayer.setCoin(dataBet.balance);

            this.betLogs.push({amount: data.r.amount, gate: data.r.gate, flag: this.flagLog});
        });
    }

    finishBetting() {
        if (this.nodeDisableFinishBet.active) {
            return;
        }

        BlackjackSignalRClient.getInstance().send('FinishBetting', [], (data) => {
            if (data.c < 0) {
                this.showToast(BlackjackSignalRClient.getErrMsg(data.c));
                return;
            }

            this.mePlayer.showReady();
        });
    }

    closePlay() {
        BlackjackSignalRClient.getInstance().send('ExitRoom', [], (data) => {
            if (data.c == 0) {
                BlackjackSignalRClient.getInstance().dontReceive();
                App.instance.gotoLobby();
                return;
            }

            if (data.r.status) {
                this.mePlayer.showRegisterQuit();
                this.showToast(App.instance.getTextLang('me8'));
            } else {
                this.mePlayer.hideRegisterQuit();
                this.showToast(App.instance.getTextLang('me9'));
            }
        })
    }

    placeBet(player: BlackJackPlayer, amount: number) {
        player.updateBetAmount(amount);

        const chipPrefab = this.getChipByAmount(amount) || this.getChipByAmount(this.minBet);
        const chip = instantiate(chipPrefab);
        chip.setScale(new Vec3(0.5, 0.5, 0.5));
        this.chipContainer.addChild(chip);

        const avatarWorldPos = player.avatarNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0, 0, 0));
        const statusWorldPos = player.statusNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0, 0, 0));

        const startPos = this.node.getComponent(UITransform).convertToNodeSpaceAR(avatarWorldPos);
        let endPos = this.node.getComponent(UITransform).convertToNodeSpaceAR(statusWorldPos);

        const tolerance = 10;
        const existingChips = this.chipContainer.children.filter(child =>
            Vec3.distance(child.getPosition(), endPos) <= tolerance
        );

        if (existingChips.length > 0) {
            const offsetX = 10 * (existingChips.length % 5);
            const offsetY = -10 * Math.floor(existingChips.length / 5);
            endPos.x += offsetX;
            endPos.y += offsetY;
        }

        chip.setPosition(startPos);
        chip.name = player.id;

        tween(chip)
            .to(0.5, { position: endPos }, { easing: 'sineOut' })
            .start();
    }

    showToast(msg: string) {
        this.lblToast.string = msg;
        this.lblToast.node.active = true;

        setTimeout(() => {
            if (this.lblToast.node) {
                this.lblToast.node.active = false;
            }
        }, 2000);
    }

    chatInGame: any = null;

    showBoxChat() {
        App.instance.inactivityTimer = 0;
        if (this.chatInGame == null) {
            BundleControl.loadPrefabPopup("prefabs/ChatInGame", (prefab: any) => {
                this.chatInGame = instantiate(prefab).getComponent("ChatInGame");
                this.node.addChild(this.chatInGame.node);
                this.chatInGame.show(Configs.InGameIds.BlackJack);
            });
        } else {
            this.chatInGame.show(Configs.InGameIds.BlackJack);
        }
    }

    actShowPopupRank() {
        let popupRank = instantiate(this.popupRank);
        this.popupContainer.addChild(popupRank);
        popupRank.getComponent(CasinoPopupRank).showDetail(this.currency, Configs.InGameIds.BlackJack);
    }

    private moveChipToPlayer(chip: Node, player: BlackJackPlayer, moveFromDealerFirst: boolean = false) {
        const targetPos = player.node.getPosition().clone();
        const middlePos = chip.getPosition().clone();

        if (moveFromDealerFirst) {
            const delayStep = 0.05;

            for (let i = 0; i < 10; i++) {
                const cloneChip = instantiate(chip);
                cloneChip.setPosition(this.dealerNode.getPosition());
                this.chipContainer.addChild(cloneChip);

                tween(cloneChip)
                    .delay(i * delayStep)
                    .to(0.3, { position: middlePos }, { easing: 'sineOut' })
                    .call(() => cloneChip.destroy())
                    .start();
            }

            const totalDelay = 10 * delayStep;

            const originalBetNode = player.betAmount.children[0];
            const betAmountNode = instantiate(originalBetNode);

            const worldPos = originalBetNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0, 0, 0));
            const localPos = this.chipContainer.getComponent(UITransform).convertToNodeSpaceAR(worldPos);

            betAmountNode.setPosition(localPos);
            this.chipContainer.addChild(betAmountNode);

            tween(betAmountNode)
                .delay(totalDelay)
                .call(() => {
                    player.betAmount.active = false;
                })
                .to(0.3, { position: targetPos }, { easing: 'sineOut' })
                .call(() => {
                    betAmountNode.destroy();
                })
                .start();

            tween(chip)
                .delay(totalDelay + 0.1)
                .to(0.4, { position: targetPos }, { easing: 'sineOut' })
                .call(() => chip.destroy())
                .start();

            return;
        }

        tween(chip)
            .to(0.5, { position: targetPos }, { easing: 'sineOut' })
            .call(() => chip.destroy())
            .start();
    }

    private moveChipToDealer(chip: Node, player: BlackJackPlayer = null) {
        const dealerPos = this.dealerNode.getPosition();

        if (player) {
            const originalBetNode = player.betAmount.children[0];
            const betAmountNode = instantiate(originalBetNode);

            const worldPos = originalBetNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0, 0, 0));
            const localPos = this.chipContainer.getComponent(UITransform).convertToNodeSpaceAR(worldPos);

            betAmountNode.setPosition(localPos);
            this.chipContainer.addChild(betAmountNode);

            tween(betAmountNode)
                .call(() => {
                    player.betAmount.active = false;
                })
                .to(0.3, { position: dealerPos }, { easing: 'sineOut' })
                .call(() => {
                    betAmountNode.destroy();
                })
                .start();
        }

        tween(chip)
            .to(0.5, { position: dealerPos }, { easing: 'sineOut' })
            .call(() => chip.destroy())
            .start();
    }

    private cardActionQueue: Promise<any> = Promise.resolve();

    private showDealerCardsAsync(startIndex: number, cards: number[]): Promise<void> {
        return new Promise((resolve) => {
            let count = cards.length - startIndex;

            if (count === 0) {
                resolve();
                return;
            }

            for (let i = startIndex; i < cards.length; i++) {
                const cardNode = instantiate(this.dealerCard);
                cardNode.name = i.toString();
                cardNode.active = false;
                cardNode.getChildByName('arrow').active = false;

                this.scheduleOnce(() => {
                    this.dealerCards.addChild(cardNode);
                    this.moveCardFromMachine(cardNode, cards[i]);

                    count--;
                    if (count === 0) {
                        resolve();
                    }
                }, (i - startIndex));
            }
        });
    }

    setupHandDealer(cards: number[]) {
        this.cardActionQueue = this.cardActionQueue.then(async () => {
            this.dealerCardContainer.active = true;
            this.dealerCards.removeAllChildren();
            this.lblDealerSum.node.parent.active = false;
            this.dealerCardBlackjack.active = false;

            await this.showDealerCardsAsync(0, cards);
            this.lastCardIdx = cards.length - 1;
        });
    }

    updateHandDealer(cards: number[], bestSum: number) {
        this.cardActionQueue = this.cardActionQueue.then(async () => {
            if (this.dealerCards.children.length > 0) {
                this.dealerCards.children[0].getChildByName('arrow').active = true;
            }

            if (this.lastCardIdx === 1) {
                this.dealerCards.getChildByName('1')?.destroy();
            }

            const revealTime = cards.length - this.lastCardIdx;

            await this.showDealerCardsAsync(this.lastCardIdx, cards);
            this.lastCardIdx = cards.length;

            this.scheduleOnce(() => {
                this.lblDealerSum.node.parent.active = true;
                this.lblDealerSum.string = bestSum.toString();

                if (bestSum > 21) {
                    this.dealerCards.children.forEach((cardNode) => {
                        cardNode.getChildByName('mask').active = true;
                    });
                    this.lblDealerSum.string = App.instance.getTextLang('ca221');
                }

                if (bestSum === 21 && this.dealerCards.children.length === 2) {
                    this.dealerCardBlackjack.active = true;
                }
            }, revealTime);
        });
    }

    moveCardFromMachine(card: Node, cardIndex: number = -1, player: BlackJackPlayer = null, isSplit: boolean = false) {
        const cardParent = card.parent;
        const layout = cardParent?.getComponent(Layout);

        if (layout) {
            layout.enabled = false;
            layout.updateLayout();
        }

        player?.hideTurnMain();
        player?.hideTurnSplit();

        const worldPos = this.blackjackMachine.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0, 0, 0));
        const startPos = cardParent.getComponent(UITransform).convertToNodeSpaceAR(worldPos);
        const endPos = card.getPosition().clone();

        const sprite = card.getChildByName('sprite');
        if (sprite) {
            const spriteComp = sprite.getComponent(Sprite);
            if (spriteComp) spriteComp.spriteFrame = this.cardBack;
        } else {
            const spriteComp = card.getComponent(Sprite);
            if (spriteComp) spriteComp.spriteFrame = this.cardBack;
        }

        card.setPosition(startPos);
        card.active = true;

        tween(card)
            .to(0.5, { position: endPos }, { easing: 'sineOut' })
            .call(() => {
                if (cardIndex !== -1) {
                    this.flipCard(card, cardIndex);
                    if (player && player.meTurn) {
                        if (isSplit) {
                            if (cardParent.children.length > 1) {
                                player.showTurnSplit();
                            }
                        } else {
                            player.showTurnMain();
                        }
                    }
                }
                if (layout) {
                    layout.enabled = true;
                    layout.updateLayout();
                }
            })
            .start();
    }

    private flipCard(card: Node, cardIndex: number) {
        tween(card)
            .to(0.2, { scale: new Vec3(0, 1, 1) })
            .call(() => {
                const sprite = card.getChildByName('sprite');
                const spriteComp = sprite
                    ? sprite.getComponent(Sprite)
                    : card.getComponent(Sprite);
                if (spriteComp) {
                    spriteComp.spriteFrame = this.cardFronts[cardIndex];
                }
            })
            .to(0.3, { scale: new Vec3(1, 1, 1) })
            .start();
    }

    showGuide() {
        this.guideBG.active = true;
        this.guide.active = true;
    }

    hideGuide() {
        this.guideBG.active = false;
        this.guide.active = false;
    }
}

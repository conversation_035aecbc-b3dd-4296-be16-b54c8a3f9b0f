[{"__type__": "cc.AnimationClip", "_name": "OpenPig", "_objFlags": 0, "_native": "", "sample": 60, "speed": 0.8, "wrapMode": 1, "_duration": 0.3333333333333333, "_hash": 0, "_tracks": [{"__id__": 1}, {"__id__": 12}, {"__id__": 23}], "_events": [], "_exoticAnimation": null}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 2}}, "_channels": [{"__id__": 4}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 3}, "scale"]}, {"__type__": "cc.animation.HierarchyPath", "path": "light_effect"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 5}}, {"__type__": "cc.RealCurve", "_times": [0, 0.016666666666666666, 0.03333333333333333, 0.05, 0.2, 0.3333333333333333], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1.2, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1.5, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 7}}, {"__type__": "cc.RealCurve", "_times": [0, 0.016666666666666666, 0.03333333333333333, 0.05, 0.2, 0.3333333333333333], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1.2, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1.5, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 9}}, {"__type__": "cc.RealCurve", "_times": [0, 0.016666666666666666, 0.03333333333333333, 0.05, 0.2, 0.3333333333333333], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 11}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 13}}, "_channels": [{"__id__": 15}, {"__id__": 17}, {"__id__": 19}, {"__id__": 21}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 14}, "scale"]}, {"__type__": "cc.animation.HierarchyPath", "path": "gold"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 16}}, {"__type__": "cc.RealCurve", "_times": [0.25, 0.3], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 18}}, {"__type__": "cc.RealCurve", "_times": [0.25, 0.3], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 20}}, {"__type__": "cc.RealCurve", "_times": [0.25, 0.3], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 22}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 24}}, "_channels": [{"__id__": 26}, {"__id__": 28}, {"__id__": 30}, {"__id__": 32}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 25}, "scale"]}, {"__type__": "cc.animation.HierarchyPath", "path": "frame"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 27}}, {"__type__": "cc.RealCurve", "_times": [0, 0.016666666666666666, 0.25], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 29}}, {"__type__": "cc.RealCurve", "_times": [0, 0.016666666666666666, 0.25], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 31}}, {"__type__": "cc.RealCurve", "_times": [0, 0.016666666666666666, 0.25], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 33}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}]